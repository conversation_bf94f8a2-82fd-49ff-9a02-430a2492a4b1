import{b as a,c}from"./chunk-AHJUNP3B.js";import{b as r,c as s}from"./chunk-OFX7WKKZ.js";import{a as i}from"./chunk-F4H6ZFEG.js";import"./chunk-WTCPO44B.js";import{g as n}from"./chunk-2R6CW7ES.js";var h=()=>{let e=window;e.addEventListener("statusTap",()=>{r(()=>{let m=e.innerWidth,d=e.innerHeight,o=document.elementFromPoint(m/2,d/2);if(!o)return;let t=a(o);t&&new Promise(l=>i(t,l)).then(()=>{s(()=>n(void 0,null,function*(){t.style.setProperty("--overflow","hidden"),yield c(t,300),t.style.removeProperty("--overflow")}))})})})};export{h as startStatusTap};
