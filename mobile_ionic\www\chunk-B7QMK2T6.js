import{b as R}from"./chunk-CCKNKBVQ.js";import{a as P}from"./chunk-FULEFYAM.js";import"./chunk-AGHLTJ5J.js";import{$ as I,$a as F,A as M,C as f,D as c,Db as j,E as v,F as r,G as o,H as g,I as C,J as p,K as u,M as l,N as _,Na as A,O as k,Oa as z,U as O,V as N,W as w,Wa as $,X as T,ab as B,ea as S,oa as E,q as b,qb as D,r as h,ub as L,vb as V,x as y,y as n,z as x}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as m}from"./chunk-2R6CW7ES.js";function U(i,d){if(i&1&&(r(0,"span",17),l(1),o()),i&2){let t=u();n(),_(t.unreadCount)}}function q(i,d){if(i&1){let t=C();r(0,"div",18)(1,"span",19),l(2,"Earlier"),o(),r(3,"button",20),p("click",function(){b(t);let a=u();return h(a.seeAllNotifications())}),l(4,"See all"),o()()}}function H(i,d){i&1&&(r(0,"div",21),g(1,"ion-icon",22),r(2,"h3"),l(3,"No notifications yet"),o(),r(4,"p"),l(5,"When you receive notifications, they'll appear here."),o()())}function W(i,d){if(i&1&&(r(0,"span",36),l(1),o()),i&2){let t=u().$implicit;n(),k(" ",t.reactions," Reactions ")}}function Y(i,d){i&1&&g(0,"div",37)}function G(i,d){if(i&1){let t=C();r(0,"div",23),p("click",function(){let a=b(t).$implicit,s=u();return h(s.onNotificationClick(a))}),r(1,"div",24),g(2,"img",25),r(3,"div",26),g(4,"ion-icon",27),o()(),r(5,"div",28)(6,"div",29)(7,"span",30),l(8),o(),r(9,"span",31),l(10),o()(),r(11,"div",32)(12,"span",33),l(13),o(),f(14,W,2,1,"span",34),o()(),f(15,Y,1,0,"div",35),o()}if(i&2){let t=d.$implicit,e=u();v("unread",!t.read),n(2),c("src",e.getNotificationIcon(t),y)("alt",t.type),n(),c("ngClass",e.getIconBadgeClass(t)),n(),c("name",e.getBadgeIcon(t)),n(4),_(e.getNotificationTitle(t)),n(2),_(e.getNotificationDescription(t)),n(3),_(e.getTimeAgo(t.created_at)),n(),c("ngIf",t.reactions),n(),c("ngIf",!t.read)}}function J(i,d){i&1&&g(0,"ion-spinner",42)}function K(i,d){i&1&&(r(0,"span"),l(1,"Load More"),o())}function Q(i,d){if(i&1){let t=C();r(0,"div",38)(1,"ion-button",39),p("click",function(){b(t);let a=u();return h(a.loadMoreNotifications())}),f(2,J,1,0,"ion-spinner",40)(3,K,2,0,"span",41),o()()}if(i&2){let t=u();n(),c("disabled",t.isLoading),n(),c("ngIf",t.isLoading),n(),c("ngIf",!t.isLoading)}}var st=(()=>{class i{constructor(t,e,a){this.router=t,this.http=e,this.fcmService=a,this.notifications=[],this.filteredNotifications=[],this.activeTab="all",this.unreadCount=0,this.isLoading=!1,this.hasMoreNotifications=!1,this.currentPage=1,this.notificationSubscription=null}ngOnInit(){this.loadNotifications(),this.subscribeToNewNotifications()}ngOnDestroy(){this.notificationSubscription&&this.notificationSubscription.unsubscribe()}loadNotifications(){return m(this,null,function*(){this.isLoading=!0;try{let t=yield this.http.get(`${P.apiUrl}/notifications?page=${this.currentPage}`).toPromise();t&&(this.currentPage===1?this.notifications=t.notifications:this.notifications.push(...t.notifications),this.unreadCount=t.unread_count,this.hasMoreNotifications=t.has_more,this.filterNotifications())}catch(t){console.error("Error loading notifications:",t)}finally{this.isLoading=!1}})}subscribeToNewNotifications(){this.notificationSubscription=this.fcmService.notifications$.subscribe(t=>{let e={id:Date.now(),type:this.mapFCMTypeToAppType(t.category),title:t.title,message:t.body,data:t,read:!1,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};this.notifications.unshift(e),this.unreadCount++,this.filterNotifications()})}mapFCMTypeToAppType(t){switch(t?.toLowerCase()){case"evacuation":case"evacuation_center":return"evacuation_center_added";case"emergency":case"earthquake":case"typhoon":case"flood":return"emergency_alert";case"system":return"system_update";default:return"general"}}setActiveTab(t){this.activeTab=t,this.filterNotifications()}filterNotifications(){this.activeTab==="unread"?this.filteredNotifications=this.notifications.filter(t=>!t.read):this.filteredNotifications=this.notifications}onNotificationClick(t){return m(this,null,function*(){switch(t.read||(yield this.markAsRead(t)),t.type){case"evacuation_center_added":this.router.navigate(["/tabs/map"],{queryParams:{disasterType:"all",showNewCenters:!0}});break;case"emergency_alert":let e=this.extractDisasterType(t);this.router.navigate(["/tabs/map"],{queryParams:{disasterType:e}});break;default:break}})}extractDisasterType(t){let e=t.message.toLowerCase();return e.includes("earthquake")?"Earthquake":e.includes("typhoon")?"Typhoon":e.includes("flood")?"Flood":"all"}markAsRead(t){return m(this,null,function*(){try{yield this.http.put(`${P.apiUrl}/notifications/${t.id}/read`,{}).toPromise(),t.read=!0,this.unreadCount=Math.max(0,this.unreadCount-1),this.filterNotifications()}catch(e){console.error("Error marking notification as read:",e)}})}markAllAsRead(){return m(this,null,function*(){try{yield this.http.put(`${P.apiUrl}/notifications/mark-all-read`,{}).toPromise(),this.notifications.forEach(t=>t.read=!0),this.unreadCount=0,this.filterNotifications()}catch(t){console.error("Error marking all notifications as read:",t)}})}loadMoreNotifications(){!this.isLoading&&this.hasMoreNotifications&&(this.currentPage++,this.loadNotifications())}seeAllNotifications(){this.setActiveTab("all")}goBack(){this.router.navigate(["/tabs/home"])}trackByNotificationId(t,e){return e.id}getNotificationIcon(t){switch(t.type){case"evacuation_center_added":return"assets/evacuation-center-icon.png";case"emergency_alert":return"assets/emergency-icon.png";case"system_update":return"assets/system-icon.png";default:return"assets/alerto_icon.png"}}getBadgeIcon(t){switch(t.type){case"evacuation_center_added":return"add-circle";case"emergency_alert":return"warning";case"system_update":return"settings";default:return"notifications"}}getIconBadgeClass(t){switch(t.type){case"evacuation_center_added":return"badge-success";case"emergency_alert":return"badge-danger";case"system_update":return"badge-info";default:return"badge-primary"}}getNotificationTitle(t){switch(t.type){case"evacuation_center_added":return"New evacuation center added.";case"emergency_alert":return t.title;default:return t.title}}getNotificationDescription(t){return t.message}getTimeAgo(t){let e=new Date(t),s=Math.floor((new Date().getTime()-e.getTime())/1e3);return s<60?`${s}s`:s<3600?`${Math.floor(s/60)}m`:s<86400?`${Math.floor(s/3600)}h`:s<604800?`${Math.floor(s/86400)}d`:`${Math.floor(s/604800)}w`}static{this.\u0275fac=function(e){return new(e||i)(x(S),x(I),x(R))}}static{this.\u0275cmp=M({type:i,selectors:[["app-notifications"]],decls:23,vars:13,consts:[[3,"translucent"],["slot","start"],[3,"click"],["name","chevron-back-outline"],["slot","end"],[3,"click","disabled"],["name","checkmark-done-outline"],[3,"fullscreen"],[1,"notification-header"],[1,"notification-tabs"],[1,"tab-button",3,"click"],["class","unread-badge",4,"ngIf"],["class","section-header",4,"ngIf"],[1,"notifications-container"],["class","no-notifications",4,"ngIf"],["class","notification-item",3,"unread","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","load-more-container",4,"ngIf"],[1,"unread-badge"],[1,"section-header"],[1,"section-title"],[1,"see-all-btn",3,"click"],[1,"no-notifications"],["name","notifications-outline",1,"no-notifications-icon"],[1,"notification-item",3,"click"],[1,"notification-icon"],[1,"icon-image",3,"src","alt"],[1,"icon-badge",3,"ngClass"],[3,"name"],[1,"notification-content"],[1,"notification-text"],[1,"notification-title"],[1,"notification-description"],[1,"notification-meta"],[1,"notification-time"],["class","notification-reactions",4,"ngIf"],["class","unread-indicator",4,"ngIf"],[1,"notification-reactions"],[1,"unread-indicator"],[1,"load-more-container"],["fill","clear",3,"click","disabled"],["name","crescent",4,"ngIf"],[4,"ngIf"],["name","crescent"]],template:function(e,a){e&1&&(r(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1)(3,"ion-button",2),p("click",function(){return a.goBack()}),g(4,"ion-icon",3),o()(),r(5,"ion-title"),l(6,"Notifications"),o(),r(7,"ion-buttons",4)(8,"ion-button",5),p("click",function(){return a.markAllAsRead()}),g(9,"ion-icon",6),o()()()(),r(10,"ion-content",7)(11,"div",8)(12,"div",9)(13,"button",10),p("click",function(){return a.setActiveTab("all")}),l(14," All "),o(),r(15,"button",10),p("click",function(){return a.setActiveTab("unread")}),l(16," Unread "),f(17,U,2,1,"span",11),o()(),f(18,q,5,0,"div",12),o(),r(19,"div",13),f(20,H,6,0,"div",14)(21,G,16,11,"div",15),o(),f(22,Q,4,3,"div",16),o()),e&2&&(c("translucent",!0),n(8),c("disabled",a.unreadCount===0),n(2),c("fullscreen",!0),n(3),v("active",a.activeTab==="all"),n(2),v("active",a.activeTab==="unread"),n(2),c("ngIf",a.unreadCount>0),n(),c("ngIf",a.filteredNotifications.length>0),n(2),c("ngIf",a.filteredNotifications.length===0),n(),c("ngForOf",a.filteredNotifications)("ngForTrackBy",a.trackByNotificationId),n(),c("ngIf",a.hasMoreNotifications))},dependencies:[j,A,z,$,F,B,D,L,V,T,O,N,w,E],styles:['@charset "UTF-8";ion-content[_ngcontent-%COMP%]{--background: #f0f2f5}.notification-header[_ngcontent-%COMP%]{background:#fff;padding:16px;border-bottom:1px solid #e4e6ea;position:sticky;top:0;z-index:10}.notification-tabs[_ngcontent-%COMP%]{display:flex;gap:16px;margin-bottom:16px}.tab-button[_ngcontent-%COMP%]{background:none;border:none;font-size:16px;font-weight:600;color:#65676b;padding:8px 12px;border-radius:20px;cursor:pointer;transition:all .2s ease;position:relative}.tab-button.active[_ngcontent-%COMP%]{color:#1877f2;background:#e7f3ff}.tab-button[_ngcontent-%COMP%]:hover{background:#f2f3f4}.unread-badge[_ngcontent-%COMP%]{background:#e41e3f;color:#fff;font-size:12px;font-weight:600;padding:2px 6px;border-radius:10px;margin-left:6px;min-width:18px;text-align:center}.section-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.section-title[_ngcontent-%COMP%]{font-size:17px;font-weight:600;color:#050505}.see-all-btn[_ngcontent-%COMP%]{background:none;border:none;color:#1877f2;font-size:15px;font-weight:500;cursor:pointer;padding:4px 8px;border-radius:6px}.see-all-btn[_ngcontent-%COMP%]:hover{background:#f2f3f4}.notifications-container[_ngcontent-%COMP%]{padding:0}.no-notifications[_ngcontent-%COMP%]{text-align:center;padding:60px 20px;color:#65676b}.no-notifications[_ngcontent-%COMP%]   .no-notifications-icon[_ngcontent-%COMP%]{font-size:64px;color:#bcc0c4;margin-bottom:16px}.no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;margin:0 0 8px;color:#050505}.no-notifications[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px;margin:0;line-height:1.4}.notification-item[_ngcontent-%COMP%]{background:#fff;padding:12px 16px;border-bottom:1px solid #e4e6ea;display:flex;align-items:flex-start;gap:12px;cursor:pointer;transition:background-color .2s ease;position:relative}.notification-item[_ngcontent-%COMP%]:hover{background:#f7f8fa}.notification-item.unread[_ngcontent-%COMP%]{background:#f0f8ff}.notification-item.unread[_ngcontent-%COMP%]:hover{background:#e7f3ff}.notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-icon[_ngcontent-%COMP%]{position:relative;flex-shrink:0}.icon-image[_ngcontent-%COMP%]{width:56px;height:56px;border-radius:50%;object-fit:cover;border:2px solid #e4e6ea}.icon-badge[_ngcontent-%COMP%]{position:absolute;bottom:-2px;right:-2px;width:24px;height:24px;border-radius:50%;display:flex;align-items:center;justify-content:center;border:2px solid white;font-size:12px}.icon-badge.badge-success[_ngcontent-%COMP%]{background:#42b883;color:#fff}.icon-badge.badge-danger[_ngcontent-%COMP%]{background:#e41e3f;color:#fff}.icon-badge.badge-info[_ngcontent-%COMP%]{background:#1877f2;color:#fff}.icon-badge.badge-primary[_ngcontent-%COMP%]{background:#03b2dd;color:#fff}.icon-badge[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:14px}.notification-content[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-text[_ngcontent-%COMP%]{margin-bottom:4px;line-height:1.3}.notification-title[_ngcontent-%COMP%]{color:#050505;font-size:15px;font-weight:400;display:block;margin-bottom:2px}.notification-description[_ngcontent-%COMP%]{color:#65676b;font-size:13px;display:block;line-height:1.4}.notification-meta[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;font-size:13px;color:#65676b}.notification-time[_ngcontent-%COMP%]{font-weight:500}.notification-reactions[_ngcontent-%COMP%]:before{content:"\\2022";margin-right:8px}.unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:16px;transform:translateY(-50%);width:8px;height:8px;background:#1877f2;border-radius:50%}.load-more-container[_ngcontent-%COMP%]{padding:20px;text-align:center;background:#fff;border-top:1px solid #e4e6ea}.load-more-container[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{--color: #1877f2;font-weight:600}@media (prefers-color-scheme: dark){ion-content[_ngcontent-%COMP%]{--background: #18191a}.notification-header[_ngcontent-%COMP%]{background:#242526;border-bottom-color:#3a3b3c}.tab-button[_ngcontent-%COMP%]{color:#b0b3b8}.tab-button.active[_ngcontent-%COMP%]{color:#2d88ff;background:#263951}.tab-button[_ngcontent-%COMP%]:hover{background:#3a3b3c}.section-title[_ngcontent-%COMP%]{color:#e4e6ea}.see-all-btn[_ngcontent-%COMP%]{color:#2d88ff}.see-all-btn[_ngcontent-%COMP%]:hover{background:#3a3b3c}.notification-item[_ngcontent-%COMP%]{background:#242526;border-bottom-color:#3a3b3c}.notification-item[_ngcontent-%COMP%]:hover{background:#3a3b3c}.notification-item.unread[_ngcontent-%COMP%]{background:#263951}.notification-item.unread[_ngcontent-%COMP%]:hover{background:#2d4373}.notification-title[_ngcontent-%COMP%]{color:#e4e6ea}.notification-description[_ngcontent-%COMP%], .notification-meta[_ngcontent-%COMP%], .no-notifications[_ngcontent-%COMP%]{color:#b0b3b8}.no-notifications[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#e4e6ea}.load-more-container[_ngcontent-%COMP%]{background:#242526;border-top-color:#3a3b3c}}']})}}return i})();export{st as NotificationsPage};
