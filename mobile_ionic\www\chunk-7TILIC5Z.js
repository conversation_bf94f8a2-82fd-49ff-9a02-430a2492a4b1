import{b as n}from"./chunk-GIGBYVJT.js";import{b as r}from"./chunk-QVY4QQUF.js";import{b as d,f as a,g as i}from"./chunk-2HRRFJKF.js";import"./chunk-BAKMWPBW.js";import"./chunk-2R6CW7ES.js";var e=":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}",s=e,c=":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}",l=c,k=(()=>{let o=class{constructor(t){d(this,t)}render(){return a(i,{key:"998217066084f966bf5d356fed85bcbd451f675a",class:r(this)},a("slot",{key:"1a6f7c9d4dc6a875f86b5b3cda6d59cb39587f22"}))}};return o.style={ios:s,md:l},o})(),p=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}",g=p,b=":host{--background:var(--ion-color-primary, #0054e9);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}",h=b,w=(()=>{let o=class{constructor(t){d(this,t),this.color=void 0}render(){let t=r(this);return a(i,{key:"1a2d39c5deec771a2f2196447627b62a7d4c8389",class:n(this.color,{[t]:!0})},a("slot",{key:"fc1b6587f1ed24715748eb6785e7fb7a57cdd5cd"}))}};return o.style={ios:g,md:h},o})(),f=":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}",v=f,I=(()=>{let o=class{constructor(t){d(this,t)}render(){return a(i,{key:"7f5fd6c056da2d82feb2c3c33f3e6dec898787f5",class:r(this)},a("slot",{key:"d15fd2b6cdc03777edc1930be95ad838e1b376c8"}))}};return o.style=v,o})();export{k as ion_avatar,w as ion_badge,I as ion_thumbnail};
