import{a as y}from"./chunk-FULEFYAM.js";import{a as G}from"./chunk-AGHLTJ5J.js";import{$ as S,Cb as D,Da as I,a as P,b as A,d as N,e as x,ea as E,i as F,m as w,o as h,s as M,zb as R}from"./chunk-QCXYQNJC.js";import{a as v,g as l}from"./chunk-2R6CW7ES.js";function L(){if(typeof process>"u"){var o=typeof window<"u"?window:{},r=5e3,e=Date.now(),t=!1;o.document.addEventListener("deviceready",function(){console.log("Ionic Native: deviceready event fired after "+(Date.now()-e)+" ms"),t=!0}),setTimeout(function(){!t&&o.cordova&&console.warn("Ionic Native: deviceready did not fire within "+r+"ms. This can happen when plugins are in an inconsistent state. Try removing plugins from plugins/ and reinstalling them.")},r)}}var U={error:"cordova_not_available"},W={error:"plugin_not_installed"};function b(o){var r=function(){if(Promise)return new Promise(function(i,a){o(i,a)});console.error("No Promise support or polyfill found. To enable Ionic Native support, please add the es6-promise polyfill before this script, or run with a library like Angular or on a recent browser.")};if(typeof window<"u"&&window.angular){var e=window.document,t=window.angular.element(e.querySelector("[ng-app]")||e.body).injector();if(t){var n=t.get("$q");return n(function(i,a){o(i,a)})}console.warn("Angular 1 was detected but $q couldn't be retrieved. This is usually when the app is not bootstrapped on the html or body tag. Falling back to native promises which won't trigger an automatic digest when promises resolve.")}return r()}function _(o,r,e,t){t===void 0&&(t={});var n,i,a=b(function(s,c){t.destruct?n=g(o,r,e,t,function(){for(var m=[],f=0;f<arguments.length;f++)m[f]=arguments[f];return s(m)},function(){for(var m=[],f=0;f<arguments.length;f++)m[f]=arguments[f];return c(m)}):n=g(o,r,e,t,s,c),i=c});return n&&n.error&&(a.catch(function(){}),typeof i=="function"&&i(n.error)),a}function H(o,r,e,t){return t===void 0&&(t={}),b(function(n,i){var a=g(o,r,e,t);a?a.error?i(a.error):a.then&&a.then(n).catch(i):i({error:"unexpected_error"})})}function J(o,r,e,t){return t===void 0&&(t={}),new P(function(n){var i;return t.destruct?i=g(o,r,e,t,function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return n.next(a)},function(){for(var a=[],s=0;s<arguments.length;s++)a[s]=arguments[s];return n.error(a)}):i=g(o,r,e,t,n.next.bind(n),n.error.bind(n)),i&&i.error&&(n.error(i.error),n.complete()),function(){try{if(t.clearFunction)return t.clearWithArgs?g(o,t.clearFunction,e,t,n.next.bind(n),n.error.bind(n)):g(o,t.clearFunction,[])}catch(a){console.warn("Unable to clear the previous observable watch for",o.constructor.getPluginName(),r),console.warn(a)}}})}function O(o,r){return r=typeof window<"u"&&r?q(window,r):r||(typeof window<"u"?window:{}),F(r,o)}function p(o,r,e){var t,n;typeof o=="string"?t=o:(t=o.constructor.getPluginRef(),e=o.constructor.getPluginName(),n=o.constructor.getPluginInstallName());var i=k(t);return!i||r&&typeof i[r]>"u"?typeof window>"u"||!window.cordova?(Z(e,r),U):(z(e,n,r),W):!0}function Y(o,r,e,t){if(r===void 0&&(r={}),r.sync)return o;if(r.callbackOrder==="reverse")o.unshift(t),o.unshift(e);else if(r.callbackStyle==="node")o.push(function(s,c){s?t(s):e(c)});else if(r.callbackStyle==="object"&&r.successName&&r.errorName){var n={};n[r.successName]=e,n[r.errorName]=t,o.push(n)}else if(typeof r.successIndex<"u"||typeof r.errorIndex<"u"){var i=function(){r.successIndex>o.length?o[r.successIndex]=e:o.splice(r.successIndex,0,e)},a=function(){r.errorIndex>o.length?o[r.errorIndex]=t:o.splice(r.errorIndex,0,t)};r.successIndex>r.errorIndex?(a(),i()):(i(),a())}else o.push(e),o.push(t);return o}function g(o,r,e,t,n,i){t===void 0&&(t={}),e=Y(e,t,n,i);var a=p(o,r);if(a===!0){var s=k(o.constructor.getPluginRef());return s[r].apply(s,e)}else return a}function k(o){return typeof window<"u"?q(window,o):null}function q(o,r){for(var e=r.split("."),t=o,n=0;n<e.length;n++){if(!t)return null;t=t[e[n]]}return t}function z(o,r,e){console.warn(e?"Native: tried calling "+o+"."+e+", but the "+o+" plugin is not installed.":"Native: tried accessing the "+o+" plugin but it's not installed."),r&&console.warn("Install the "+o+" plugin: 'ionic cordova plugin add "+r+"'")}function Z(o,r){typeof process>"u"&&console.warn(r?"Native: tried calling "+o+"."+r+", but Cordova is not available. Make sure to include cordova.js or run in a device/simulator":"Native: tried accessing the "+o+" plugin but Cordova is not available. Make sure to include cordova.js or run in a device/simulator")}var C=function(o,r,e){return e===void 0&&(e={}),function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.sync?g(o,r,t,e):e.observable?J(o,r,t,e):e.eventObservable&&e.event?O(e.event,e.element):e.otherPromise?H(o,r,t,e):_(o,r,t,e)}};function B(o,r){for(var e=r.split("."),t=o,n=0;n<e.length;n++){if(!t)return null;t=t[e[n]]}return t}var T=function(){function o(){}return o.installed=function(){var r=p(this.pluginRef)===!0;return r},o.getPlugin=function(){return typeof window<"u"?B(window,this.pluginRef):null},o.getPluginName=function(){var r=this.pluginName;return r},o.getPluginRef=function(){var r=this.pluginRef;return r},o.getPluginInstallName=function(){var r=this.plugin;return r},o.getSupportedPlatforms=function(){var r=this.platforms;return r},o.pluginName="",o.pluginRef="",o.plugin="",o.repo="",o.platforms=[],o.install="",o}();function u(o,r,e,t){return C(o,r,e).apply(this,t)}L();var $=function(o){N(r,o);function r(){return o!==null&&o.apply(this,arguments)||this}return r.prototype.getAPNSToken=function(){return u(this,"getAPNSToken",{},arguments)},r.prototype.getToken=function(){return u(this,"getToken",{},arguments)},r.prototype.onTokenRefresh=function(){return u(this,"onTokenRefresh",{observable:!0},arguments)},r.prototype.subscribeToTopic=function(e){return u(this,"subscribeToTopic",{},arguments)},r.prototype.unsubscribeFromTopic=function(e){return u(this,"unsubscribeFromTopic",{},arguments)},r.prototype.hasPermission=function(){return u(this,"hasPermission",{},arguments)},r.prototype.onNotification=function(){return u(this,"onNotification",{observable:!0,successIndex:0,errorIndex:2},arguments)},r.prototype.clearAllNotifications=function(){return u(this,"clearAllNotifications",{},arguments)},r.prototype.requestPushPermissionIOS=function(e){return u(this,"requestPushPermissionIOS",{},arguments)},r.prototype.createNotificationChannelAndroid=function(e){return u(this,"createNotificationChannelAndroid",{},arguments)},r.\u0275fac=(()=>{let e;return function(n){return(e||(e=M(r)))(n||r)}})(),r.\u0275prov=w({token:r,factory:r.\u0275fac}),r.pluginName="FCM",r.plugin="cordova-plugin-fcm-with-dependecy-updated",r.pluginRef="FCMPlugin",r.repo="https://github.com/andrehtissot/cordova-plugin-fcm-with-dependecy-updated",r.platforms=["Android","iOS"],r=x([],r),r}(T);var d=G("FirebaseMessaging",{web:()=>import("./chunk-5TZR3RXB.js").then(o=>new o.FirebaseMessagingWeb)});var qe=(()=>{class o{constructor(e,t,n,i,a,s){this.fcm=e,this.http=t,this.platform=n,this.toastCtrl=i,this.alertCtrl=a,this.router=s,this.notificationSubject=new A,this.notifications$=this.notificationSubject.asObservable()}initPush(){return l(this,null,function*(){try{if(this.platform.is("cordova")||this.platform.is("capacitor")){if(console.log("Initializing FCM..."),this.platform.is("android")&&(yield this.createAndroidNotificationChannels()),(yield this.checkGooglePlayServices())?localStorage.removeItem("google_play_services_missing"):(console.warn("Google Play Services not available. FCM may not work properly."),this.alertCtrl.create({header:"Google Play Services Required",message:"This app requires Google Play Services for push notifications. Please install or update Google Play Services and restart the app.",buttons:["OK"]}).then(t=>t.present()),localStorage.setItem("google_play_services_missing","true")),this.platform.is("capacitor"))try{let t=yield d.requestPermissions();console.log("FCM permission result:",t),t.receive==="granted"?(console.log("FCM permission granted"),console.log("Device registered with FCM")):(console.warn("FCM permission not granted:",t.receive),this.alertCtrl.create({header:"Notification Permission Required",message:"This app requires notification permissions to alert you about emergencies. Please enable notifications for this app in your device settings.",buttons:["OK"]}).then(n=>n.present()))}catch(t){console.error("Error initializing Capacitor Firebase Messaging:",t)}try{let t=yield this.getToken();console.log("FCM Token registered:",t.substring(0,20)+"..."),this.registerTokenWithBackend(t)}catch(t){console.error("Error getting FCM token:",t)}try{this.platform.is("cordova")&&this.fcm.onTokenRefresh().subscribe({next:t=>{console.log("FCM Token refreshed (Cordova):",t),this.registerTokenWithBackend(t)},error:t=>{console.error("Error in token refresh (Cordova):",t)}}),this.platform.is("capacitor")&&d.addListener("tokenReceived",t=>{console.log("FCM Token refreshed (Capacitor):",t.token),this.registerTokenWithBackend(t.token)})}catch(t){console.error("Failed to set up token refresh:",t)}this.setupNotificationListeners()}else console.log("FCM not initialized: not running on a device")}catch(e){console.error("Error in initPush:",e)}})}getToken(){return l(this,null,function*(){if(this.platform.is("capacitor"))try{let e=yield d.getToken();return console.log("Got FCM token from Capacitor Firebase Messaging:",e.token),e.token}catch(e){console.error("Error getting token from Capacitor Firebase Messaging:",e);try{let t=yield this.fcm.getToken();return console.log("Got FCM token from Cordova FCM plugin:",t),t}catch(t){throw console.error("Error getting token from Cordova FCM plugin:",t),t}}else{if(this.platform.is("cordova"))return this.fcm.getToken();{let e="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("Using mock FCM token for browser:",e),Promise.resolve(e)}}})}registerTokenWithBackend(e,t){if(localStorage.getItem("fcm_token")===e){console.log("Token already registered, skipping registration");return}let i="web";this.platform.is("ios")?i="ios":this.platform.is("android")&&(i="android"),console.log(`Registering ${i} token with backend...`);let a={token:e,device_type:i,project_id:y.firebase.projectId||"last-5acaf"};if(t)a.user_id=t,console.log(`Associating token with user ID: ${t}`);else{let s=localStorage.getItem("token");if(s)try{let c=this.parseJwt(s);c&&c.sub&&(a.user_id=c.sub,console.log(`Associating token with user ID from JWT: ${c.sub}`))}catch(c){console.error("Error parsing JWT token:",c)}}e&&(y.firebase.projectId||a.project_id)?(localStorage.setItem("fcm_token",e),localStorage.setItem("fcm_token_registering","true"),this.http.post(`${y.apiUrl}/device-token`,a).subscribe({next:s=>{console.log("Token registered with backend:",s),localStorage.removeItem("fcm_token_registering")},error:s=>{console.error("Error registering token:",s),localStorage.removeItem("fcm_token_registering")}})):(console.log("Skipping token registration: Missing project ID or token"),e&&localStorage.setItem("fcm_token",e))}parseJwt(e){try{let n=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),i=decodeURIComponent(atob(n).split("").map(function(a){return"%"+("00"+a.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(i)}catch(t){return console.error("Error parsing JWT token:",t),null}}setupNotificationListeners(){this.setupCapacitorNotificationListeners(),this.setupCordovaNotificationListeners()}setupCapacitorNotificationListeners(){try{this.platform.is("capacitor")&&(console.log("Setting up Capacitor Firebase Messaging notification listeners"),d.addListener("notificationReceived",e=>{console.log("Capacitor: Notification received in foreground:",e);let t=e.notification.data||{};this.processNotification(v({title:e.notification.title||"",body:e.notification.body||"",category:t.category||"",severity:t.severity||"low",wasTapped:!1,notification_id:t.notification_id||null,time:new Date().toISOString()},Object.keys(t).filter(n=>!["category","severity","notification_id"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))}),d.addListener("notificationActionPerformed",e=>{console.log("Capacitor: Notification tapped:",e);let t=e.notification.data||{};this.processNotification(v({title:e.notification.title||"",body:e.notification.body||"",category:t.category||"",severity:t.severity||"low",wasTapped:!0,notification_id:t.notification_id||null,time:new Date().toISOString()},Object.keys(t).filter(n=>!["category","severity","notification_id"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))}))}catch(e){console.error("Failed to set up Capacitor notification listeners:",e)}}setupCordovaNotificationListeners(){try{this.platform.is("cordova")&&(console.log("Setting up Cordova FCM notification listeners"),this.fcm.onNotification().subscribe({next:e=>{console.log("Cordova FCM notification received:",e);let t=v({},e);this.processNotification(v({title:e.title||e.aps&&e.aps.alert&&e.aps.alert.title||"",body:e.body||e.aps&&e.aps.alert&&e.aps.alert.body||e.message||"",category:e.category||"",severity:e.severity||"low",wasTapped:e.wasTapped||!1,notification_id:e.notification_id||null,time:e.time||new Date().toISOString()},Object.keys(t).filter(n=>!["title","body","category","severity","wasTapped","notification_id","time"].includes(n)).reduce((n,i)=>(n[i]=t[i],n),{})))},error:e=>{console.error("Error in Cordova FCM notification subscription:",e)}}))}catch(e){console.error("Failed to set up Cordova FCM notification listeners:",e)}}processNotification(e){try{console.log("Processed notification:",{title:e.title,body:e.body,category:e.category,severity:e.severity,wasTapped:e.wasTapped,notification_id:e.notification_id,project_id:y.firebase.projectId||"new-firebase-project"}),this.notificationSubject.next(e),e.wasTapped?(console.log("Notification tapped in background"),this.handleBackgroundNotification(e)):(console.log("Notification received in foreground"),this.handleForegroundNotification(e))}catch(t){console.error("Error processing notification:",t,e)}}handleForegroundNotification(e){return l(this,null,function*(){try{console.log("\u{1F514} Handling foreground notification:",e),this.vibrateDevice(),this.playNotificationSound(),yield new Promise(t=>setTimeout(t,500)),yield this.showEmergencyNotificationModal(e)}catch(t){console.error("\u274C Error handling foreground notification:",t),yield this.showFallbackToast(e)}})}showEmergencyNotificationModal(e){return l(this,null,function*(){try{console.log("\u{1F4F1} Creating emergency notification modal...",e),yield new Promise(i=>setTimeout(i,100));let t=yield this.alertCtrl.getTop();t&&(console.log("\u26A0\uFE0F Alert already present, dismissing first..."),yield t.dismiss(),yield new Promise(i=>setTimeout(i,500))),console.log("\u{1F35E} Showing immediate toast notification as backup..."),yield this.showFallbackToast(e);let n=yield this.alertCtrl.create({header:e.title||"EMERGENCY ALERT",subHeader:e.category?`${e.category.toUpperCase()} ALERT`:"",message:e.body||"",buttons:[{text:"Go to Safe Area",cssClass:"alert-button-primary",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from modal"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",cssClass:"alert-button-secondary",handler:()=>(console.log("\u274C User dismissed notification modal"),!0)}],cssClass:`emergency-notification ${e.category?.toLowerCase()||"general"}-alert`,backdropDismiss:!1,keyboardClose:!1});console.log("\u2705 Emergency modal created, presenting..."),yield n.present(),console.log("\u2705 Emergency modal presented successfully")}catch(t){console.error("\u274C Error creating emergency modal:",t),console.error("\u274C Modal error details:",t?.message,t?.stack),yield this.showFallbackToast(e)}})}showFallbackToast(e){return l(this,null,function*(){try{console.log("\u{1F35E} Showing emergency toast notification"),yield(yield this.toastCtrl.create({header:`\u{1F6A8} ${e.title||"EMERGENCY ALERT"}`,message:e.body||"Emergency notification received",duration:8e3,position:"top",color:this.getToastColor(e.category),cssClass:"emergency-toast",buttons:[{text:"\u{1F5FA}\uFE0F Go to Safe Area",handler:()=>(console.log("\u{1F5FA}\uFE0F User tapped Go to Safe Area from toast"),this.navigateBasedOnNotification(e),!0)},{text:"Dismiss",role:"cancel",handler:()=>(console.log("\u274C User dismissed toast notification"),!0)}]})).present(),console.log("\u2705 Emergency toast shown successfully"),this.vibrateDevice()}catch(t){console.error("\u274C Even fallback toast failed:",t);try{yield(yield this.alertCtrl.create({header:"\u{1F6A8} EMERGENCY ALERT",message:`${e.title}

${e.body}`,buttons:[{text:"Go to Safe Area",handler:()=>this.navigateBasedOnNotification(e)},"Dismiss"]})).present(),console.log("\u2705 Simple alert shown as last resort")}catch(n){console.error("\u274C All notification methods failed:",n),console.log("\u{1F4E2} EMERGENCY NOTIFICATION (all display methods failed):",e)}}})}getToastColor(e){if(!e)return"warning";switch(e.toLowerCase()){case"earthquake":return"warning";case"flood":return"primary";case"typhoon":return"success";case"fire":return"danger";default:return"warning"}}simulateForegroundNotification(e){return l(this,null,function*(){console.log("\u{1F9EA} Simulating foreground notification:",e),yield this.processNotification(e)})}simulateBackgroundNotification(e){return l(this,null,function*(){console.log("\u{1F9EA} Simulating background notification:",e),e.wasTapped=!0,yield this.processNotification(e)})}getDisasterStyle(e){if(!e)return{color:"#666666",icon:"notifications-outline"};let t=e.toLowerCase();return t.includes("earthquake")||t.includes("quake")?{color:"#ffa500",icon:"earth-outline"}:t.includes("flood")||t.includes("flash")?{color:"#0000ff",icon:"water-outline"}:t.includes("typhoon")||t.includes("storm")||t.includes("hurricane")?{color:"#008000",icon:"thunderstorm-outline"}:t.includes("fire")?{color:"#ff0000",icon:"flame-outline"}:{color:"#666666",icon:"alert-circle-outline"}}vibrateDevice(){"vibrate"in navigator?(navigator.vibrate([1e3,200,1e3,200,1e3]),console.log("Device vibration triggered with strong pattern")):console.log("Vibration API not supported on this device")}playNotificationSound(){try{let e=new Audio;e.src="data:audio/wav;base64,UklGRl9vT19XQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YU...",e.volume=1,e.play().catch(t=>{console.error("Error playing notification sound:",t)})}catch(e){console.error("Error creating audio element:",e)}}handleBackgroundNotification(e){return l(this,null,function*(){try{console.log("\u{1F514} Handling background notification tap:",e),this.vibrateDevice(),yield new Promise(t=>setTimeout(t,1e3)),yield this.showEmergencyNotificationModal(e)}catch(t){console.error("\u274C Error handling background notification:",t),this.navigateBasedOnNotification(e)}})}navigateBasedOnNotification(e){if(console.log("\u{1F5FA}\uFE0F Navigating based on notification:",e),e.category==="evacuation_center"||e.data&&e.data.type==="evacuation_center_added"){console.log("\u{1F3E2} New evacuation center notification detected"),this.handleEvacuationCenterNotification(e);return}if(e.category){let t=e.category.toLowerCase();console.log("\u{1F4CD} Notification category:",t);let n="";switch(t){case"flood":case"flashflood":n="Flood";break;case"earthquake":case"quake":n="Earthquake";break;case"typhoon":case"storm":case"hurricane":n="Typhoon";break;case"fire":n="Fire";break;default:console.warn("Unknown disaster category:",t),n="all";break}if(console.log(`\u{1F5FA}\uFE0F Mapped disaster type: ${t} -> ${n}`),n&&n!=="all"){console.log("\u{1F5FA}\uFE0F Navigating to disaster-specific map:",n);let i="";switch(n.toLowerCase()){case"earthquake":i="/earthquake-map";break;case"typhoon":i="/typhoon-map";break;case"flood":i="/flood-map";break;default:i="/tabs/map";break}console.log(`\u{1F5FA}\uFE0F Navigating to route: ${i}`),this.router.navigate([i])}else console.log("\u{1F3E0} Navigating to home (unknown disaster type)"),this.router.navigate(["/tabs/home"])}else console.log("\u{1F3E0} Navigating to home (no category)"),this.router.navigate(["/tabs/home"])}handleEvacuationCenterNotification(e){console.log("\u{1F3E2} Handling evacuation center notification:",e);try{let t=null;if(e.data&&(t=e.data),t&&t.evacuation_center_id){let n=t.evacuation_center_id,i=t.disaster_type,a=t.location?.latitude,s=t.location?.longitude;console.log("\u{1F3E2} Evacuation center details:",{id:n,disasterType:i,lat:a,lng:s});let c="/all-maps";if(i)switch(i.toLowerCase()){case"earthquake":c="/earthquake-map";break;case"typhoon":c="/typhoon-map";break;case"flood":case"flash flood":c="/flood-map";break;default:c="/all-maps";break}console.log(`\u{1F5FA}\uFE0F Navigating to ${c} for new evacuation center`),this.router.navigate([c],{queryParams:{newCenterId:n,highlightCenter:"true",centerLat:a,centerLng:s}})}else console.log("\u{1F3E2} No evacuation center data found, navigating to all maps"),this.router.navigate(["/all-maps"])}catch(t){console.error("\u274C Error handling evacuation center notification:",t),this.router.navigate(["/all-maps"])}}checkGooglePlayServices(){return l(this,null,function*(){try{if(this.platform.is("capacitor")&&this.platform.is("android"))try{return yield d.getToken(),!0}catch(e){console.error("Error checking Google Play Services:",e);let t=e.message||"";return!(t.includes("Google Play Services")||t.includes("GoogleApiAvailability")||t.includes("API unavailable"))}return!0}catch(e){return console.error("Error in checkGooglePlayServices:",e),!0}})}createAndroidNotificationChannels(){return l(this,null,function*(){try{this.platform.is("android")&&(console.log("Creating Android notification channels"),yield this.sendTestChannelNotification("emergency-alerts","Emergency Alerts","High priority notifications for emergencies","high"),yield this.sendTestChannelNotification("general-notifications","General Notifications","Standard notifications","default"),console.log("Android notification channels created successfully"))}catch(e){console.error("Error creating Android notification channels:",e)}})}sendTestChannelNotification(e,t,n,i){return l(this,null,function*(){try{let a={notification:{title:"Channel Setup",body:"Setting up notification channels",android:{channelId:e,priority:i==="high"?"high":i==="default"?"default":"low",sound:i!=="low",vibrate:i!=="low",visibility:"public"}}};console.log(`Created notification channel: ${e} (${t})`)}catch(a){console.error(`Error creating notification channel ${e}:`,a)}})}refreshFCMToken(e){return l(this,null,function*(){try{if(console.log("Refreshing FCM token..."),this.platform.is("capacitor"))try{yield d.deleteToken(),console.log("Existing FCM token deleted");let t=yield d.getToken();return console.log("New FCM token obtained:",t.token),this.registerTokenWithBackend(t.token,e),!0}catch(t){return console.error("Error refreshing Capacitor FCM token:",t),!1}else if(this.platform.is("cordova"))try{let t=yield this.fcm.getToken();return console.log("New FCM token obtained from Cordova:",t),this.registerTokenWithBackend(t,e),!0}catch(t){return console.error("Error refreshing Cordova FCM token:",t),!1}else{let t="browser-mock-token-"+Math.random().toString(36).substring(2,15);return console.log("New mock FCM token generated:",t),this.registerTokenWithBackend(t,e),!0}}catch(t){return console.error("Error in refreshFCMToken:",t),!1}})}static{this.\u0275fac=function(t){return new(t||o)(h($),h(S),h(I),h(D),h(R),h(E))}}static{this.\u0275prov=w({token:o,factory:o.\u0275fac,providedIn:"root"})}}return o})();export{$ as a,qe as b};
