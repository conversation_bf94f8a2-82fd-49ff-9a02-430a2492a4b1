import{b as Re}from"./chunk-AGHLTJ5J.js";import{a as Oe,b as Ne,g as a}from"./chunk-2R6CW7ES.js";var xe=()=>{};var Be=function(e){let t=[],n=0;for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);i<128?t[n++]=i:i<2048?(t[n++]=i>>6|192,t[n++]=i&63|128):(i&64512)===55296&&r+1<e.length&&(e.charCodeAt(r+1)&64512)===56320?(i=65536+((i&1023)<<10)+(e.charCodeAt(++r)&1023),t[n++]=i>>18|240,t[n++]=i>>12&63|128,t[n++]=i>>6&63|128,t[n++]=i&63|128):(t[n++]=i>>12|224,t[n++]=i>>6&63|128,t[n++]=i&63|128)}return t},Bt=function(e){let t=[],n=0,r=0;for(;n<e.length;){let i=e[n++];if(i<128)t[r++]=String.fromCharCode(i);else if(i>191&&i<224){let o=e[n++];t[r++]=String.fromCharCode((i&31)<<6|o&63)}else if(i>239&&i<365){let o=e[n++],s=e[n++],c=e[n++],f=((i&7)<<18|(o&63)<<12|(s&63)<<6|c&63)-65536;t[r++]=String.fromCharCode(55296+(f>>10)),t[r++]=String.fromCharCode(56320+(f&1023))}else{let o=e[n++],s=e[n++];t[r++]=String.fromCharCode((i&15)<<12|(o&63)<<6|s&63)}}return t.join("")},Pe={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:typeof atob=="function",encodeByteArray(e,t){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();let n=t?this.byteToCharMapWebSafe_:this.byteToCharMap_,r=[];for(let i=0;i<e.length;i+=3){let o=e[i],s=i+1<e.length,c=s?e[i+1]:0,f=i+2<e.length,d=f?e[i+2]:0,u=o>>2,h=(o&3)<<4|c>>4,O=(c&15)<<2|d>>6,B=d&63;f||(B=64,s||(O=64)),r.push(n[u],n[h],n[O],n[B])}return r.join("")},encodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?btoa(e):this.encodeByteArray(Be(e),t)},decodeString(e,t){return this.HAS_NATIVE_SUPPORT&&!t?atob(e):Bt(this.decodeStringToByteArray(e,t))},decodeStringToByteArray(e,t){this.init_();let n=t?this.charToByteMapWebSafe_:this.charToByteMap_,r=[];for(let i=0;i<e.length;){let o=n[e.charAt(i++)],c=i<e.length?n[e.charAt(i)]:0;++i;let d=i<e.length?n[e.charAt(i)]:64;++i;let h=i<e.length?n[e.charAt(i)]:64;if(++i,o==null||c==null||d==null||h==null)throw new Y;let O=o<<2|c>>4;if(r.push(O),d!==64){let B=c<<4&240|d>>2;if(r.push(B),h!==64){let Mt=d<<6&192|h;r.push(Mt)}}}return r},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}},Y=class extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}},Pt=function(e){let t=Be(e);return Pe.encodeByteArray(t,!0)},X=function(e){return Pt(e).replace(/\./g,"")},Le=function(e){try{return Pe.decodeString(e,!0)}catch(t){console.error("base64Decode failed: ",t)}return null};function Lt(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("Unable to locate global object.")}var Ft=()=>Lt().__FIREBASE_DEFAULTS__,$t=()=>{if(typeof process>"u"||typeof process.env>"u")return;let e=process.env.__FIREBASE_DEFAULTS__;if(e)return JSON.parse(e)},jt=()=>{if(typeof document>"u")return;let e;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch{return}let t=e&&Le(e[1]);return t&&JSON.parse(t)},Ht=()=>{try{return xe()||Ft()||$t()||jt()}catch(e){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);return}};var Q=()=>{var e;return(e=Ht())===null||e===void 0?void 0:e.config};var P=class{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((t,n)=>{this.resolve=t,this.reject=n})}wrapCallback(t){return(n,r)=>{n?this.reject(n):this.resolve(r),typeof t=="function"&&(this.promise.catch(()=>{}),t.length===1?t(n):t(n,r))}}};function L(){try{return typeof indexedDB=="object"}catch{return!1}}function F(){return new Promise((e,t)=>{try{let n=!0,r="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(r);i.onsuccess=()=>{i.result.close(),n||self.indexedDB.deleteDatabase(r),e(!0)},i.onupgradeneeded=()=>{n=!1},i.onerror=()=>{var o;t(((o=i.error)===null||o===void 0?void 0:o.message)||"")}}catch(n){t(n)}})}function Fe(){return!(typeof navigator>"u"||!navigator.cookieEnabled)}var Ut="FirebaseError",b=class e extends Error{constructor(t,n,r){super(n),this.code=t,this.customData=r,this.name=Ut,Object.setPrototypeOf(this,e.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,w.prototype.create)}},w=class{constructor(t,n,r){this.service=t,this.serviceName=n,this.errors=r}create(t,...n){let r=n[0]||{},i=`${this.service}/${t}`,o=this.errors[t],s=o?Vt(o,r):"Error",c=`${this.serviceName}: ${s} (${i}).`;return new b(i,c,r)}};function Vt(e,t){return e.replace(Wt,(n,r)=>{let i=t[r];return i!=null?String(i):`<${r}?>`})}var Wt=/\{\$([^}]+)}/g;function $(e,t){if(e===t)return!0;let n=Object.keys(e),r=Object.keys(t);for(let i of n){if(!r.includes(i))return!1;let o=e[i],s=t[i];if(Me(o)&&Me(s)){if(!$(o,s))return!1}else if(o!==s)return!1}for(let i of r)if(!n.includes(i))return!1;return!0}function Me(e){return e!==null&&typeof e=="object"}var yi=4*60*60*1e3;function R(e){return e&&e._delegate?e._delegate:e}var g=class{constructor(t,n,r){this.name=t,this.instanceFactory=n,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}};var S="[DEFAULT]";var Z=class{constructor(t,n){this.name=t,this.container=n,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(t){let n=this.normalizeInstanceIdentifier(t);if(!this.instancesDeferred.has(n)){let r=new P;if(this.instancesDeferred.set(n,r),this.isInitialized(n)||this.shouldAutoInitialize())try{let i=this.getOrInitializeService({instanceIdentifier:n});i&&r.resolve(i)}catch{}}return this.instancesDeferred.get(n).promise}getImmediate(t){var n;let r=this.normalizeInstanceIdentifier(t?.identifier),i=(n=t?.optional)!==null&&n!==void 0?n:!1;if(this.isInitialized(r)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:r})}catch(o){if(i)return null;throw o}else{if(i)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(t){if(t.name!==this.name)throw Error(`Mismatching Component ${t.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=t,!!this.shouldAutoInitialize()){if(Kt(t))try{this.getOrInitializeService({instanceIdentifier:S})}catch{}for(let[n,r]of this.instancesDeferred.entries()){let i=this.normalizeInstanceIdentifier(n);try{let o=this.getOrInitializeService({instanceIdentifier:i});r.resolve(o)}catch{}}}}clearInstance(t=S){this.instancesDeferred.delete(t),this.instancesOptions.delete(t),this.instances.delete(t)}delete(){return a(this,null,function*(){let t=Array.from(this.instances.values());yield Promise.all([...t.filter(n=>"INTERNAL"in n).map(n=>n.INTERNAL.delete()),...t.filter(n=>"_delete"in n).map(n=>n._delete())])})}isComponentSet(){return this.component!=null}isInitialized(t=S){return this.instances.has(t)}getOptions(t=S){return this.instancesOptions.get(t)||{}}initialize(t={}){let{options:n={}}=t,r=this.normalizeInstanceIdentifier(t.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let i=this.getOrInitializeService({instanceIdentifier:r,options:n});for(let[o,s]of this.instancesDeferred.entries()){let c=this.normalizeInstanceIdentifier(o);r===c&&s.resolve(i)}return i}onInit(t,n){var r;let i=this.normalizeInstanceIdentifier(n),o=(r=this.onInitCallbacks.get(i))!==null&&r!==void 0?r:new Set;o.add(t),this.onInitCallbacks.set(i,o);let s=this.instances.get(i);return s&&t(s,i),()=>{o.delete(t)}}invokeOnInitCallbacks(t,n){let r=this.onInitCallbacks.get(n);if(r)for(let i of r)try{i(t,n)}catch{}}getOrInitializeService({instanceIdentifier:t,options:n={}}){let r=this.instances.get(t);if(!r&&this.component&&(r=this.component.instanceFactory(this.container,{instanceIdentifier:zt(t),options:n}),this.instances.set(t,r),this.instancesOptions.set(t,n),this.invokeOnInitCallbacks(r,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,r)}catch{}return r||null}normalizeInstanceIdentifier(t=S){return this.component?this.component.multipleInstances?t:S:t}shouldAutoInitialize(){return!!this.component&&this.component.instantiationMode!=="EXPLICIT"}};function zt(e){return e===S?void 0:e}function Kt(e){return e.instantiationMode==="EAGER"}var j=class{constructor(t){this.name=t,this.providers=new Map}addComponent(t){let n=this.getProvider(t.name);if(n.isComponentSet())throw new Error(`Component ${t.name} has already been registered with ${this.name}`);n.setComponent(t)}addOrOverwriteComponent(t){this.getProvider(t.name).isComponentSet()&&this.providers.delete(t.name),this.addComponent(t)}getProvider(t){if(this.providers.has(t))return this.providers.get(t);let n=new Z(t,this);return this.providers.set(t,n),n}getProviders(){return Array.from(this.providers.values())}};var qt=[],l=function(e){return e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT",e}(l||{}),Gt={debug:l.DEBUG,verbose:l.VERBOSE,info:l.INFO,warn:l.WARN,error:l.ERROR,silent:l.SILENT},Jt=l.INFO,Yt={[l.DEBUG]:"log",[l.VERBOSE]:"log",[l.INFO]:"info",[l.WARN]:"warn",[l.ERROR]:"error"},Xt=(e,t,...n)=>{if(t<e.logLevel)return;let r=new Date().toISOString(),i=Yt[t];if(i)console[i](`[${r}]  ${e.name}:`,...n);else throw new Error(`Attempted to log a message with an invalid logType (value: ${t})`)},H=class{constructor(t){this.name=t,this._logLevel=Jt,this._logHandler=Xt,this._userLogHandler=null,qt.push(this)}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in l))throw new TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel=typeof t=="string"?Gt[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if(typeof t!="function")throw new TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,l.DEBUG,...t),this._logHandler(this,l.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,l.VERBOSE,...t),this._logHandler(this,l.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,l.INFO,...t),this._logHandler(this,l.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,l.WARN,...t),this._logHandler(this,l.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,l.ERROR,...t),this._logHandler(this,l.ERROR,...t)}};var Qt=(e,t)=>t.some(n=>e instanceof n),$e,je;function Zt(){return $e||($e=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])}function en(){return je||(je=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])}var He=new WeakMap,te=new WeakMap,Ue=new WeakMap,ee=new WeakMap,re=new WeakMap;function tn(e){let t=new Promise((n,r)=>{let i=()=>{e.removeEventListener("success",o),e.removeEventListener("error",s)},o=()=>{n(m(e.result)),i()},s=()=>{r(e.error),i()};e.addEventListener("success",o),e.addEventListener("error",s)});return t.then(n=>{n instanceof IDBCursor&&He.set(n,e)}).catch(()=>{}),re.set(t,e),t}function nn(e){if(te.has(e))return;let t=new Promise((n,r)=>{let i=()=>{e.removeEventListener("complete",o),e.removeEventListener("error",s),e.removeEventListener("abort",s)},o=()=>{n(),i()},s=()=>{r(e.error||new DOMException("AbortError","AbortError")),i()};e.addEventListener("complete",o),e.addEventListener("error",s),e.addEventListener("abort",s)});te.set(e,t)}var ne={get(e,t,n){if(e instanceof IDBTransaction){if(t==="done")return te.get(e);if(t==="objectStoreNames")return e.objectStoreNames||Ue.get(e);if(t==="store")return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return m(e[t])},set(e,t,n){return e[t]=n,!0},has(e,t){return e instanceof IDBTransaction&&(t==="done"||t==="store")?!0:t in e}};function Ve(e){ne=e(ne)}function rn(e){return e===IDBDatabase.prototype.transaction&&!("objectStoreNames"in IDBTransaction.prototype)?function(t,...n){let r=e.call(U(this),t,...n);return Ue.set(r,t.sort?t.sort():[t]),m(r)}:en().includes(e)?function(...t){return e.apply(U(this),t),m(He.get(this))}:function(...t){return m(e.apply(U(this),t))}}function on(e){return typeof e=="function"?rn(e):(e instanceof IDBTransaction&&nn(e),Qt(e,Zt())?new Proxy(e,ne):e)}function m(e){if(e instanceof IDBRequest)return tn(e);if(ee.has(e))return ee.get(e);let t=on(e);return t!==e&&(ee.set(e,t),re.set(t,e)),t}var U=e=>re.get(e);function A(e,t,{blocked:n,upgrade:r,blocking:i,terminated:o}={}){let s=indexedDB.open(e,t),c=m(s);return r&&s.addEventListener("upgradeneeded",f=>{r(m(s.result),f.oldVersion,f.newVersion,m(s.transaction),f)}),n&&s.addEventListener("blocked",f=>n(f.oldVersion,f.newVersion,f)),c.then(f=>{o&&f.addEventListener("close",()=>o()),i&&f.addEventListener("versionchange",d=>i(d.oldVersion,d.newVersion,d))}).catch(()=>{}),c}function V(e,{blocked:t}={}){let n=indexedDB.deleteDatabase(e);return t&&n.addEventListener("blocked",r=>t(r.oldVersion,r)),m(n).then(()=>{})}var sn=["get","getKey","getAll","getAllKeys","count"],an=["put","add","delete","clear"],ie=new Map;function We(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&typeof t=="string"))return;if(ie.get(t))return ie.get(t);let n=t.replace(/FromIndex$/,""),r=t!==n,i=an.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!(i||sn.includes(n)))return;let o=function(s,...c){return a(this,null,function*(){let f=this.transaction(s,i?"readwrite":"readonly"),d=f.store;return r&&(d=d.index(c.shift())),(yield Promise.all([d[n](...c),i&&f.done]))[0]})};return ie.set(t,o),o}Ve(e=>Ne(Oe({},e),{get:(t,n,r)=>We(t,n)||e.get(t,n,r),has:(t,n)=>!!We(t,n)||e.has(t,n)}));var se=class{constructor(t){this.container=t}getPlatformInfoString(){return this.container.getProviders().map(n=>{if(cn(n)){let r=n.getImmediate();return`${r.library}/${r.version}`}else return null}).filter(n=>n).join(" ")}};function cn(e){let t=e.getComponent();return t?.type==="VERSION"}var ae="@firebase/app",ze="0.12.1";var v=new H("@firebase/app"),un="@firebase/app-compat",fn="@firebase/analytics-compat",dn="@firebase/analytics",ln="@firebase/app-check-compat",hn="@firebase/app-check",pn="@firebase/auth",gn="@firebase/auth-compat",mn="@firebase/database",bn="@firebase/data-connect",wn="@firebase/database-compat",yn="@firebase/functions",vn="@firebase/functions-compat",_n="@firebase/installations",En="@firebase/installations-compat",In="@firebase/messaging",Sn="@firebase/messaging-compat",An="@firebase/performance",Tn="@firebase/performance-compat",Dn="@firebase/remote-config",Cn="@firebase/remote-config-compat",kn="@firebase/storage",On="@firebase/storage-compat",Nn="@firebase/firestore",Rn="@firebase/vertexai",xn="@firebase/firestore-compat",Mn="firebase";var ce="[DEFAULT]",Bn={[ae]:"fire-core",[un]:"fire-core-compat",[dn]:"fire-analytics",[fn]:"fire-analytics-compat",[hn]:"fire-app-check",[ln]:"fire-app-check-compat",[pn]:"fire-auth",[gn]:"fire-auth-compat",[mn]:"fire-rtdb",[bn]:"fire-data-connect",[wn]:"fire-rtdb-compat",[yn]:"fire-fn",[vn]:"fire-fn-compat",[_n]:"fire-iid",[En]:"fire-iid-compat",[In]:"fire-fcm",[Sn]:"fire-fcm-compat",[An]:"fire-perf",[Tn]:"fire-perf-compat",[Dn]:"fire-rc",[Cn]:"fire-rc-compat",[kn]:"fire-gcs",[On]:"fire-gcs-compat",[Nn]:"fire-fst",[xn]:"fire-fst-compat",[Rn]:"fire-vertex","fire-js":"fire-js",[Mn]:"fire-js-all"};var W=new Map,Pn=new Map,ue=new Map;function Ke(e,t){try{e.container.addComponent(t)}catch(n){v.debug(`Component ${t.name} failed to register with FirebaseApp ${e.name}`,n)}}function _(e){let t=e.name;if(ue.has(t))return v.debug(`There were multiple attempts to register component ${t}.`),!1;ue.set(t,e);for(let n of W.values())Ke(n,e);for(let n of Pn.values())Ke(n,e);return!0}function M(e,t){let n=e.container.getProvider("heartbeat").getImmediate({optional:!0});return n&&n.triggerHeartbeat(),e.container.getProvider(t)}var Ln={"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."},I=new w("app","Firebase",Ln);var fe=class{constructor(t,n,r){this._isDeleted=!1,this._options=Object.assign({},t),this._config=Object.assign({},n),this._name=n.name,this._automaticDataCollectionEnabled=n.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new g("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(t){this.checkDestroyed(),this._automaticDataCollectionEnabled=t}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(t){this._isDeleted=t}checkDestroyed(){if(this.isDeleted)throw I.create("app-deleted",{appName:this._name})}};function Fn(e,t={}){let n=e;typeof t!="object"&&(t={name:t});let r=Object.assign({name:ce,automaticDataCollectionEnabled:!1},t),i=r.name;if(typeof i!="string"||!i)throw I.create("bad-app-name",{appName:String(i)});if(n||(n=Q()),!n)throw I.create("no-options");let o=W.get(i);if(o){if($(n,o.options)&&$(r,o.config))return o;throw I.create("duplicate-app",{appName:i})}let s=new j(i);for(let f of ue.values())s.addComponent(f);let c=new fe(n,r,s);return W.set(i,c),c}function he(e=ce){let t=W.get(e);if(!t&&e===ce&&Q())return Fn();if(!t)throw I.create("no-app",{appName:e});return t}function y(e,t,n){var r;let i=(r=Bn[e])!==null&&r!==void 0?r:e;n&&(i+=`-${n}`);let o=i.match(/\s|\//),s=t.match(/\s|\//);if(o||s){let c=[`Unable to register library "${i}" with version "${t}":`];o&&c.push(`library name "${i}" contains illegal characters (whitespace or "/")`),o&&s&&c.push("and"),s&&c.push(`version name "${t}" contains illegal characters (whitespace or "/")`),v.warn(c.join(" "));return}_(new g(`${i}-version`,()=>({library:i,version:t}),"VERSION"))}var $n="firebase-heartbeat-database",jn=1,x="firebase-heartbeat-store",oe=null;function Ye(){return oe||(oe=A($n,jn,{upgrade:(e,t)=>{switch(t){case 0:try{e.createObjectStore(x)}catch(n){console.warn(n)}}}}).catch(e=>{throw I.create("idb-open",{originalErrorMessage:e.message})})),oe}function Hn(e){return a(this,null,function*(){try{let n=(yield Ye()).transaction(x),r=yield n.objectStore(x).get(Xe(e));return yield n.done,r}catch(t){if(t instanceof b)v.warn(t.message);else{let n=I.create("idb-get",{originalErrorMessage:t?.message});v.warn(n.message)}}})}function qe(e,t){return a(this,null,function*(){try{let r=(yield Ye()).transaction(x,"readwrite");yield r.objectStore(x).put(t,Xe(e)),yield r.done}catch(n){if(n instanceof b)v.warn(n.message);else{let r=I.create("idb-set",{originalErrorMessage:n?.message});v.warn(r.message)}}})}function Xe(e){return`${e.name}!${e.options.appId}`}var Un=1024,Vn=30,de=class{constructor(t){this.container=t,this._heartbeatsCache=null;let n=this.container.getProvider("app").getImmediate();this._storage=new le(n),this._heartbeatsCachePromise=this._storage.read().then(r=>(this._heartbeatsCache=r,r))}triggerHeartbeat(){return a(this,null,function*(){var t,n;try{let i=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),o=Ge();if(((t=this._heartbeatsCache)===null||t===void 0?void 0:t.heartbeats)==null&&(this._heartbeatsCache=yield this._heartbeatsCachePromise,((n=this._heartbeatsCache)===null||n===void 0?void 0:n.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===o||this._heartbeatsCache.heartbeats.some(s=>s.date===o))return;if(this._heartbeatsCache.heartbeats.push({date:o,agent:i}),this._heartbeatsCache.heartbeats.length>Vn){let s=zn(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(s,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(r){v.warn(r)}})}getHeartbeatsHeader(){return a(this,null,function*(){var t;try{if(this._heartbeatsCache===null&&(yield this._heartbeatsCachePromise),((t=this._heartbeatsCache)===null||t===void 0?void 0:t.heartbeats)==null||this._heartbeatsCache.heartbeats.length===0)return"";let n=Ge(),{heartbeatsToSend:r,unsentEntries:i}=Wn(this._heartbeatsCache.heartbeats),o=X(JSON.stringify({version:2,heartbeats:r}));return this._heartbeatsCache.lastSentHeartbeatDate=n,i.length>0?(this._heartbeatsCache.heartbeats=i,yield this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),o}catch(n){return v.warn(n),""}})}};function Ge(){return new Date().toISOString().substring(0,10)}function Wn(e,t=Un){let n=[],r=e.slice();for(let i of e){let o=n.find(s=>s.agent===i.agent);if(o){if(o.dates.push(i.date),Je(n)>t){o.dates.pop();break}}else if(n.push({agent:i.agent,dates:[i.date]}),Je(n)>t){n.pop();break}r=r.slice(1)}return{heartbeatsToSend:n,unsentEntries:r}}var le=class{constructor(t){this.app=t,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}runIndexedDBEnvironmentCheck(){return a(this,null,function*(){return L()?F().then(()=>!0).catch(()=>!1):!1})}read(){return a(this,null,function*(){if(yield this._canUseIndexedDBPromise){let n=yield Hn(this.app);return n?.heartbeats?n:{heartbeats:[]}}else return{heartbeats:[]}})}overwrite(t){return a(this,null,function*(){var n;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return qe(this.app,{lastSentHeartbeatDate:(n=t.lastSentHeartbeatDate)!==null&&n!==void 0?n:i.lastSentHeartbeatDate,heartbeats:t.heartbeats})}else return})}add(t){return a(this,null,function*(){var n;if(yield this._canUseIndexedDBPromise){let i=yield this.read();return qe(this.app,{lastSentHeartbeatDate:(n=t.lastSentHeartbeatDate)!==null&&n!==void 0?n:i.lastSentHeartbeatDate,heartbeats:[...i.heartbeats,...t.heartbeats]})}else return})}};function Je(e){return X(JSON.stringify({version:2,heartbeats:e})).length}function zn(e){if(e.length===0)return-1;let t=0,n=e[0].date;for(let r=1;r<e.length;r++)e[r].date<n&&(n=e[r].date,t=r);return t}function Kn(e){_(new g("platform-logger",t=>new se(t),"PRIVATE")),_(new g("heartbeat",t=>new de(t),"PRIVATE")),y(ae,ze,e),y(ae,ze,"esm2017"),y("fire-js","")}Kn("");var et="@firebase/installations",be="0.6.14";var tt=1e4,nt=`w:${be}`,rt="FIS_v2",qn="https://firebaseinstallations.googleapis.com/v1",Gn=60*60*1e3,Jn="installations",Yn="Installations";var Xn={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},D=new w(Jn,Yn,Xn);function it(e){return e instanceof b&&e.code.includes("request-failed")}function ot({projectId:e}){return`${qn}/projects/${e}/installations`}function st(e){return{token:e.token,requestStatus:2,expiresIn:Zn(e.expiresIn),creationTime:Date.now()}}function at(e,t){return a(this,null,function*(){let r=(yield t.json()).error;return D.create("request-failed",{requestName:e,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})})}function ct({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}function Qn(e,{refreshToken:t}){let n=ct(e);return n.append("Authorization",er(t)),n}function ut(e){return a(this,null,function*(){let t=yield e();return t.status>=500&&t.status<600?e():t})}function Zn(e){return Number(e.replace("s","000"))}function er(e){return`${rt} ${e}`}function tr(r,i){return a(this,arguments,function*({appConfig:e,heartbeatServiceProvider:t},{fid:n}){let o=ot(e),s=ct(e),c=t.getImmediate({optional:!0});if(c){let h=yield c.getHeartbeatsHeader();h&&s.append("x-firebase-client",h)}let f={fid:n,authVersion:rt,appId:e.appId,sdkVersion:nt},d={method:"POST",headers:s,body:JSON.stringify(f)},u=yield ut(()=>fetch(o,d));if(u.ok){let h=yield u.json();return{fid:h.fid||n,registrationStatus:2,refreshToken:h.refreshToken,authToken:st(h.authToken)}}else throw yield at("Create Installation",u)})}function ft(e){return new Promise(t=>{setTimeout(t,e)})}function nr(e){return btoa(String.fromCharCode(...e)).replace(/\+/g,"-").replace(/\//g,"_")}var rr=/^[cdef][\w-]{21}$/,me="";function ir(){try{let e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;let n=or(e);return rr.test(n)?n:me}catch{return me}}function or(e){return nr(e).substr(0,22)}function K(e){return`${e.appName}!${e.appId}`}var dt=new Map;function lt(e,t){let n=K(e);ht(n,t),sr(n,t)}function ht(e,t){let n=dt.get(e);if(n)for(let r of n)r(t)}function sr(e,t){let n=ar();n&&n.postMessage({key:e,fid:t}),cr()}var T=null;function ar(){return!T&&"BroadcastChannel"in self&&(T=new BroadcastChannel("[Firebase] FID Change"),T.onmessage=e=>{ht(e.data.key,e.data.fid)}),T}function cr(){dt.size===0&&T&&(T.close(),T=null)}var ur="firebase-installations-database",fr=1,C="firebase-installations-store",pe=null;function we(){return pe||(pe=A(ur,fr,{upgrade:(e,t)=>{switch(t){case 0:e.createObjectStore(C)}}})),pe}function z(e,t){return a(this,null,function*(){let n=K(e),i=(yield we()).transaction(C,"readwrite"),o=i.objectStore(C),s=yield o.get(n);return yield o.put(t,n),yield i.done,(!s||s.fid!==t.fid)&&lt(e,t.fid),t})}function pt(e){return a(this,null,function*(){let t=K(e),r=(yield we()).transaction(C,"readwrite");yield r.objectStore(C).delete(t),yield r.done})}function q(e,t){return a(this,null,function*(){let n=K(e),i=(yield we()).transaction(C,"readwrite"),o=i.objectStore(C),s=yield o.get(n),c=t(s);return c===void 0?yield o.delete(n):yield o.put(c,n),yield i.done,c&&(!s||s.fid!==c.fid)&&lt(e,c.fid),c})}function ye(e){return a(this,null,function*(){let t,n=yield q(e.appConfig,r=>{let i=dr(r),o=lr(e,i);return t=o.registrationPromise,o.installationEntry});return n.fid===me?{installationEntry:yield t}:{installationEntry:n,registrationPromise:t}})}function dr(e){let t=e||{fid:ir(),registrationStatus:0};return gt(t)}function lr(e,t){if(t.registrationStatus===0){if(!navigator.onLine){let i=Promise.reject(D.create("app-offline"));return{installationEntry:t,registrationPromise:i}}let n={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=hr(e,n);return{installationEntry:n,registrationPromise:r}}else return t.registrationStatus===1?{installationEntry:t,registrationPromise:pr(e)}:{installationEntry:t}}function hr(e,t){return a(this,null,function*(){try{let n=yield tr(e,t);return z(e.appConfig,n)}catch(n){throw it(n)&&n.customData.serverCode===409?yield pt(e.appConfig):yield z(e.appConfig,{fid:t.fid,registrationStatus:0}),n}})}function pr(e){return a(this,null,function*(){let t=yield Qe(e.appConfig);for(;t.registrationStatus===1;)yield ft(100),t=yield Qe(e.appConfig);if(t.registrationStatus===0){let{installationEntry:n,registrationPromise:r}=yield ye(e);return r||n}return t})}function Qe(e){return q(e,t=>{if(!t)throw D.create("installation-not-found");return gt(t)})}function gt(e){return gr(e)?{fid:e.fid,registrationStatus:0}:e}function gr(e){return e.registrationStatus===1&&e.registrationTime+tt<Date.now()}function mr(r,i){return a(this,arguments,function*({appConfig:e,heartbeatServiceProvider:t},n){let o=br(e,n),s=Qn(e,n),c=t.getImmediate({optional:!0});if(c){let h=yield c.getHeartbeatsHeader();h&&s.append("x-firebase-client",h)}let f={installation:{sdkVersion:nt,appId:e.appId}},d={method:"POST",headers:s,body:JSON.stringify(f)},u=yield ut(()=>fetch(o,d));if(u.ok){let h=yield u.json();return st(h)}else throw yield at("Generate Auth Token",u)})}function br(e,{fid:t}){return`${ot(e)}/${t}/authTokens:generate`}function ve(e,t=!1){return a(this,null,function*(){let n,r=yield q(e.appConfig,o=>{if(!mt(o))throw D.create("not-registered");let s=o.authToken;if(!t&&vr(s))return o;if(s.requestStatus===1)return n=wr(e,t),o;{if(!navigator.onLine)throw D.create("app-offline");let c=Er(o);return n=yr(e,c),c}});return n?yield n:r.authToken})}function wr(e,t){return a(this,null,function*(){let n=yield Ze(e.appConfig);for(;n.authToken.requestStatus===1;)yield ft(100),n=yield Ze(e.appConfig);let r=n.authToken;return r.requestStatus===0?ve(e,t):r})}function Ze(e){return q(e,t=>{if(!mt(t))throw D.create("not-registered");let n=t.authToken;return Ir(n)?Object.assign(Object.assign({},t),{authToken:{requestStatus:0}}):t})}function yr(e,t){return a(this,null,function*(){try{let n=yield mr(e,t),r=Object.assign(Object.assign({},t),{authToken:n});return yield z(e.appConfig,r),n}catch(n){if(it(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))yield pt(e.appConfig);else{let r=Object.assign(Object.assign({},t),{authToken:{requestStatus:0}});yield z(e.appConfig,r)}throw n}})}function mt(e){return e!==void 0&&e.registrationStatus===2}function vr(e){return e.requestStatus===2&&!_r(e)}function _r(e){let t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+Gn}function Er(e){let t={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},e),{authToken:t})}function Ir(e){return e.requestStatus===1&&e.requestTime+tt<Date.now()}function Sr(e){return a(this,null,function*(){let t=e,{installationEntry:n,registrationPromise:r}=yield ye(t);return r?r.catch(console.error):ve(t).catch(console.error),n.fid})}function Ar(e,t=!1){return a(this,null,function*(){let n=e;return yield Tr(n),(yield ve(n,t)).token})}function Tr(e){return a(this,null,function*(){let{registrationPromise:t}=yield ye(e);t&&(yield t)})}function Dr(e){if(!e||!e.options)throw ge("App Configuration");if(!e.name)throw ge("App Name");let t=["projectId","apiKey","appId"];for(let n of t)if(!e.options[n])throw ge(n);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}function ge(e){return D.create("missing-app-config-values",{valueName:e})}var bt="installations",Cr="installations-internal",kr=e=>{let t=e.getProvider("app").getImmediate(),n=Dr(t),r=M(t,"heartbeat");return{app:t,appConfig:n,heartbeatServiceProvider:r,_delete:()=>Promise.resolve()}},Or=e=>{let t=e.getProvider("app").getImmediate(),n=M(t,bt).getImmediate();return{getId:()=>Sr(n),getToken:i=>Ar(n,i)}};function Nr(){_(new g(bt,kr,"PUBLIC")),_(new g(Cr,Or,"PRIVATE"))}Nr();y(et,be);y(et,be,"esm2017");var Rr="/firebase-messaging-sw.js",xr="/firebase-cloud-messaging-push-scope",St="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",Mr="https://fcmregistrations.googleapis.com/v1",At="google.c.a.c_id",Br="google.c.a.c_l",Pr="google.c.a.ts",Lr="google.c.a.e",wt=1e4;var G=function(e){return e.PUSH_RECEIVED="push-received",e.NOTIFICATION_CLICKED="notification-clicked",e}(G||{});function E(e){let t=new Uint8Array(e);return btoa(String.fromCharCode(...t)).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Fr(e){let t="=".repeat((4-e.length%4)%4),n=(e+t).replace(/\-/g,"+").replace(/_/g,"/"),r=atob(n),i=new Uint8Array(r.length);for(let o=0;o<r.length;++o)i[o]=r.charCodeAt(o);return i}var _e="fcm_token_details_db",$r=5,yt="fcm_token_object_Store";function jr(e){return a(this,null,function*(){if("databases"in indexedDB&&!(yield indexedDB.databases()).map(o=>o.name).includes(_e))return null;let t=null;return(yield A(_e,$r,{upgrade:(r,i,o,s)=>a(this,null,function*(){var c;if(i<2||!r.objectStoreNames.contains(yt))return;let f=s.objectStore(yt),d=yield f.index("fcmSenderId").get(e);if(yield f.clear(),!!d){if(i===2){let u=d;if(!u.auth||!u.p256dh||!u.endpoint)return;t={token:u.fcmToken,createTime:(c=u.createTime)!==null&&c!==void 0?c:Date.now(),subscriptionOptions:{auth:u.auth,p256dh:u.p256dh,endpoint:u.endpoint,swScope:u.swScope,vapidKey:typeof u.vapidKey=="string"?u.vapidKey:E(u.vapidKey)}}}else if(i===3){let u=d;t={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:E(u.auth),p256dh:E(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:E(u.vapidKey)}}}else if(i===4){let u=d;t={token:u.fcmToken,createTime:u.createTime,subscriptionOptions:{auth:E(u.auth),p256dh:E(u.p256dh),endpoint:u.endpoint,swScope:u.swScope,vapidKey:E(u.vapidKey)}}}}})})).close(),yield V(_e),yield V("fcm_vapid_details_db"),yield V("undefined"),Hr(t)?t:null})}function Hr(e){if(!e||!e.subscriptionOptions)return!1;let{subscriptionOptions:t}=e;return typeof e.createTime=="number"&&e.createTime>0&&typeof e.token=="string"&&e.token.length>0&&typeof t.auth=="string"&&t.auth.length>0&&typeof t.p256dh=="string"&&t.p256dh.length>0&&typeof t.endpoint=="string"&&t.endpoint.length>0&&typeof t.swScope=="string"&&t.swScope.length>0&&typeof t.vapidKey=="string"&&t.vapidKey.length>0}var Ur="firebase-messaging-database",Vr=1,k="firebase-messaging-store",Ee=null;function Ae(){return Ee||(Ee=A(Ur,Vr,{upgrade:(e,t)=>{switch(t){case 0:e.createObjectStore(k)}}})),Ee}function Tt(e){return a(this,null,function*(){let t=De(e),r=yield(yield Ae()).transaction(k).objectStore(k).get(t);if(r)return r;{let i=yield jr(e.appConfig.senderId);if(i)return yield Te(e,i),i}})}function Te(e,t){return a(this,null,function*(){let n=De(e),i=(yield Ae()).transaction(k,"readwrite");return yield i.objectStore(k).put(t,n),yield i.done,t})}function Wr(e){return a(this,null,function*(){let t=De(e),r=(yield Ae()).transaction(k,"readwrite");yield r.objectStore(k).delete(t),yield r.done})}function De({appConfig:e}){return e.appId}var zr={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."},p=new w("messaging","Messaging",zr);function Kr(e,t){return a(this,null,function*(){let n=yield ke(e),r=Ct(t),i={method:"POST",headers:n,body:JSON.stringify(r)},o;try{o=yield(yield fetch(Ce(e.appConfig),i)).json()}catch(s){throw p.create("token-subscribe-failed",{errorInfo:s?.toString()})}if(o.error){let s=o.error.message;throw p.create("token-subscribe-failed",{errorInfo:s})}if(!o.token)throw p.create("token-subscribe-no-token");return o.token})}function qr(e,t){return a(this,null,function*(){let n=yield ke(e),r=Ct(t.subscriptionOptions),i={method:"PATCH",headers:n,body:JSON.stringify(r)},o;try{o=yield(yield fetch(`${Ce(e.appConfig)}/${t.token}`,i)).json()}catch(s){throw p.create("token-update-failed",{errorInfo:s?.toString()})}if(o.error){let s=o.error.message;throw p.create("token-update-failed",{errorInfo:s})}if(!o.token)throw p.create("token-update-no-token");return o.token})}function Dt(e,t){return a(this,null,function*(){let r={method:"DELETE",headers:yield ke(e)};try{let o=yield(yield fetch(`${Ce(e.appConfig)}/${t}`,r)).json();if(o.error){let s=o.error.message;throw p.create("token-unsubscribe-failed",{errorInfo:s})}}catch(i){throw p.create("token-unsubscribe-failed",{errorInfo:i?.toString()})}})}function Ce({projectId:e}){return`${Mr}/projects/${e}/registrations`}function ke(n){return a(this,arguments,function*({appConfig:e,installations:t}){let r=yield t.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e.apiKey,"x-goog-firebase-installations-auth":`FIS ${r}`})})}function Ct({p256dh:e,auth:t,endpoint:n,vapidKey:r}){let i={web:{endpoint:n,auth:t,p256dh:e}};return r!==St&&(i.web.applicationPubKey=r),i}var Gr=7*24*60*60*1e3;function Jr(e){return a(this,null,function*(){let t=yield Qr(e.swRegistration,e.vapidKey),n={vapidKey:e.vapidKey,swScope:e.swRegistration.scope,endpoint:t.endpoint,auth:E(t.getKey("auth")),p256dh:E(t.getKey("p256dh"))},r=yield Tt(e.firebaseDependencies);if(r){if(Zr(r.subscriptionOptions,n))return Date.now()>=r.createTime+Gr?Xr(e,{token:r.token,createTime:Date.now(),subscriptionOptions:n}):r.token;try{yield Dt(e.firebaseDependencies,r.token)}catch(i){console.warn(i)}return vt(e.firebaseDependencies,n)}else return vt(e.firebaseDependencies,n)})}function Yr(e){return a(this,null,function*(){let t=yield Tt(e.firebaseDependencies);t&&(yield Dt(e.firebaseDependencies,t.token),yield Wr(e.firebaseDependencies));let n=yield e.swRegistration.pushManager.getSubscription();return n?n.unsubscribe():!0})}function Xr(e,t){return a(this,null,function*(){try{let n=yield qr(e.firebaseDependencies,t),r=Object.assign(Object.assign({},t),{token:n,createTime:Date.now()});return yield Te(e.firebaseDependencies,r),n}catch(n){throw n}})}function vt(e,t){return a(this,null,function*(){let r={token:yield Kr(e,t),createTime:Date.now(),subscriptionOptions:t};return yield Te(e,r),r.token})}function Qr(e,t){return a(this,null,function*(){let n=yield e.pushManager.getSubscription();return n||e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:Fr(t)})})}function Zr(e,t){let n=t.vapidKey===e.vapidKey,r=t.endpoint===e.endpoint,i=t.auth===e.auth,o=t.p256dh===e.p256dh;return n&&r&&i&&o}function _t(e){let t={from:e.from,collapseKey:e.collapse_key,messageId:e.fcmMessageId};return ei(t,e),ti(t,e),ni(t,e),t}function ei(e,t){if(!t.notification)return;e.notification={};let n=t.notification.title;n&&(e.notification.title=n);let r=t.notification.body;r&&(e.notification.body=r);let i=t.notification.image;i&&(e.notification.image=i);let o=t.notification.icon;o&&(e.notification.icon=o)}function ti(e,t){t.data&&(e.data=t.data)}function ni(e,t){var n,r,i,o,s;if(!t.fcmOptions&&!(!((n=t.notification)===null||n===void 0)&&n.click_action))return;e.fcmOptions={};let c=(i=(r=t.fcmOptions)===null||r===void 0?void 0:r.link)!==null&&i!==void 0?i:(o=t.notification)===null||o===void 0?void 0:o.click_action;c&&(e.fcmOptions.link=c);let f=(s=t.fcmOptions)===null||s===void 0?void 0:s.analytics_label;f&&(e.fcmOptions.analyticsLabel=f)}function ri(e){return typeof e=="object"&&!!e&&At in e}ii("AzSCbw63g1R0nCw85jG8","Iaya3yLKwmgvh7cF0q4");function ii(e,t){let n=[];for(let r=0;r<e.length;r++)n.push(e.charAt(r)),r<t.length&&n.push(t.charAt(r));return n.join("")}function oi(e){if(!e||!e.options)throw Ie("App Configuration Object");if(!e.name)throw Ie("App Name");let t=["projectId","apiKey","appId","messagingSenderId"],{options:n}=e;for(let r of t)if(!n[r])throw Ie(r);return{appName:e.name,projectId:n.projectId,apiKey:n.apiKey,appId:n.appId,senderId:n.messagingSenderId}}function Ie(e){return p.create("missing-app-config-values",{valueName:e})}var Se=class{constructor(t,n,r){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;let i=oi(t);this.firebaseDependencies={app:t,appConfig:i,installations:n,analyticsProvider:r}}_delete(){return Promise.resolve()}};function kt(e){return a(this,null,function*(){try{e.swRegistration=yield navigator.serviceWorker.register(Rr,{scope:xr}),e.swRegistration.update().catch(()=>{}),yield si(e.swRegistration)}catch(t){throw p.create("failed-service-worker-registration",{browserErrorMessage:t?.message})}})}function si(e){return a(this,null,function*(){return new Promise((t,n)=>{let r=setTimeout(()=>n(new Error(`Service worker not registered after ${wt} ms`)),wt),i=e.installing||e.waiting;e.active?(clearTimeout(r),t()):i?i.onstatechange=o=>{var s;((s=o.target)===null||s===void 0?void 0:s.state)==="activated"&&(i.onstatechange=null,clearTimeout(r),t())}:(clearTimeout(r),n(new Error("No incoming service worker found.")))})})}function ai(e,t){return a(this,null,function*(){if(!t&&!e.swRegistration&&(yield kt(e)),!(!t&&e.swRegistration)){if(!(t instanceof ServiceWorkerRegistration))throw p.create("invalid-sw-registration");e.swRegistration=t}})}function ci(e,t){return a(this,null,function*(){t?e.vapidKey=t:e.vapidKey||(e.vapidKey=St)})}function Ot(e,t){return a(this,null,function*(){if(!navigator)throw p.create("only-available-in-window");if(Notification.permission==="default"&&(yield Notification.requestPermission()),Notification.permission!=="granted")throw p.create("permission-blocked");return yield ci(e,t?.vapidKey),yield ai(e,t?.serviceWorkerRegistration),Jr(e)})}function ui(e,t,n){return a(this,null,function*(){let r=fi(t);(yield e.firebaseDependencies.analyticsProvider.get()).logEvent(r,{message_id:n[At],message_name:n[Br],message_time:n[Pr],message_device_time:Math.floor(Date.now()/1e3)})})}function fi(e){switch(e){case G.NOTIFICATION_CLICKED:return"notification_open";case G.PUSH_RECEIVED:return"notification_foreground";default:throw new Error}}function di(e,t){return a(this,null,function*(){let n=t.data;if(!n.isFirebaseMessaging)return;e.onMessageHandler&&n.messageType===G.PUSH_RECEIVED&&(typeof e.onMessageHandler=="function"?e.onMessageHandler(_t(n)):e.onMessageHandler.next(_t(n)));let r=n.data;ri(r)&&r[Lr]==="1"&&(yield ui(e,n.messageType,r))})}var Et="@firebase/messaging",It="0.12.18";var li=e=>{let t=new Se(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),e.getProvider("analytics-internal"));return navigator.serviceWorker.addEventListener("message",n=>di(t,n)),t},hi=e=>{let t=e.getProvider("messaging").getImmediate();return{getToken:r=>Ot(t,r)}};function pi(){_(new g("messaging",li,"PUBLIC")),_(new g("messaging-internal",hi,"PRIVATE")),y(Et,It),y(Et,It,"esm2017")}function N(){return a(this,null,function*(){try{yield F()}catch{return!1}return typeof window<"u"&&L()&&Fe()&&"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window&&"fetch"in window&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")})}function gi(e){return a(this,null,function*(){if(!navigator)throw p.create("only-available-in-window");return e.swRegistration||(yield kt(e)),Yr(e)})}function mi(e,t){if(!navigator)throw p.create("only-available-in-window");return e.onMessageHandler=t,()=>{e.onMessageHandler=null}}function J(e=he()){return N().then(t=>{if(!t)throw p.create("unsupported-browser")},t=>{throw p.create("indexed-db-unsupported")}),M(R(e),"messaging").getImmediate()}function Nt(e,t){return a(this,null,function*(){return e=R(e),Ot(e,t)})}function Rt(e){return e=R(e),gi(e)}function xt(e,t){return e=R(e),mi(e,t)}pi();var oo=(()=>{class e extends Re{constructor(){super(),N().then(n=>{if(!n)return;let r=J();xt(r,i=>this.handleNotificationReceived(i))})}checkPermissions(){return a(this,null,function*(){return(yield N())?{receive:this.convertNotificationPermissionToPermissionState(Notification.permission)}:{receive:"denied"}})}requestPermissions(){return a(this,null,function*(){if(!(yield N()))return{receive:"denied"};let r=yield Notification.requestPermission();return{receive:this.convertNotificationPermissionToPermissionState(r)}})}isSupported(){return a(this,null,function*(){return{isSupported:yield N()}})}getToken(n){return a(this,null,function*(){let r=J();return{token:yield Nt(r,{vapidKey:n.vapidKey,serviceWorkerRegistration:n.serviceWorkerRegistration})}})}deleteToken(){return a(this,null,function*(){let n=J();yield Rt(n)})}getDeliveredNotifications(){return a(this,null,function*(){this.throwUnavailableError()})}removeDeliveredNotifications(n){return a(this,null,function*(){this.throwUnavailableError()})}removeAllDeliveredNotifications(){return a(this,null,function*(){this.throwUnavailableError()})}subscribeToTopic(n){return a(this,null,function*(){this.throwUnavailableError()})}unsubscribeFromTopic(n){return a(this,null,function*(){this.throwUnavailableError()})}createChannel(n){return a(this,null,function*(){this.throwUnavailableError()})}deleteChannel(n){return a(this,null,function*(){this.throwUnavailableError()})}listChannels(){return a(this,null,function*(){this.throwUnavailableError()})}handleNotificationReceived(n){let i={notification:this.createNotificationResult(n)};this.notifyListeners(e.notificationReceivedEvent,i)}createNotificationResult(n){var r,i,o;return{body:(r=n.notification)===null||r===void 0?void 0:r.body,data:n.data,id:n.messageId,image:(i=n.notification)===null||i===void 0?void 0:i.image,title:(o=n.notification)===null||o===void 0?void 0:o.title}}convertNotificationPermissionToPermissionState(n){let r="prompt";switch(n){case"granted":r="granted";break;case"denied":r="denied";break}return r}throwUnavailableError(){throw this.unavailable("Not available on web.")}}return e.notificationReceivedEvent="notificationReceived",e})();export{oo as FirebaseMessagingWeb};
