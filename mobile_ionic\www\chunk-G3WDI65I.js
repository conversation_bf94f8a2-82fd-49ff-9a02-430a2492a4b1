import{a as T}from"./chunk-FULEFYAM.js";import{$ as O,g as h,m as u,o as d}from"./chunk-QCXYQNJC.js";import{a as g,b as _,g as c}from"./chunk-2R6CW7ES.js";var y=(()=>{class S{constructor(t){this.http=t,this.STORAGE_KEYS={EVACUATION_CENTERS:"offline_evacuation_centers",ROUTES:"offline_routes",MAP_TILES:"offline_map_tiles",LAST_SYNC:"last_data_sync",OFFLINE_MODE:"offline_mode_enabled",USER_LOCATION:"last_user_location"},this.MAX_STORAGE_SIZE=50*1024*1024,this.TILE_CACHE_LIMIT=1e3,this.initializeStorage()}initializeStorage(){localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS)||localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS,JSON.stringify([])),localStorage.getItem(this.STORAGE_KEYS.ROUTES)||localStorage.setItem(this.STORAGE_KEYS.ROUTES,JSON.stringify([])),localStorage.getItem(this.STORAGE_KEYS.MAP_TILES)||localStorage.setItem(this.STORAGE_KEYS.MAP_TILES,JSON.stringify({})),console.log("\u2705 Offline storage initialized")}syncEvacuationCenters(){return c(this,null,function*(){try{console.log("\u{1F504} Syncing evacuation centers from backend...");let t=yield h(this.http.get(`${T.apiUrl}/offline/evacuation-centers`));return t.success&&t.data?(yield this.saveEvacuationCenters(t.data),localStorage.setItem(this.STORAGE_KEYS.LAST_SYNC,t.sync_timestamp),console.log(`\u2705 Synced ${t.count} evacuation centers`),!0):(console.error("\u274C Invalid response from server"),!1)}catch(t){return console.error("\u274C Failed to sync evacuation centers:",t),!1}})}saveEvacuationCenters(t){return c(this,null,function*(){try{let e=t.map(r=>_(g({},r),{last_updated:new Date().toISOString()}));localStorage.setItem(this.STORAGE_KEYS.EVACUATION_CENTERS,JSON.stringify(e)),console.log(`\u{1F4BE} Saved ${t.length} evacuation centers to local storage`)}catch(e){throw console.error("\u274C Error saving evacuation centers:",e),e}})}getEvacuationCenters(t){return c(this,null,function*(){try{let e=localStorage.getItem(this.STORAGE_KEYS.EVACUATION_CENTERS);if(!e)return[];let r=JSON.parse(e);return t?r.filter(o=>o.disaster_type===t):r}catch(e){return console.error("\u274C Error fetching evacuation centers:",e),[]}})}getNearestCenters(t,e,r,o=2){return c(this,null,function*(){return(yield this.getEvacuationCenters(r)).map(a=>_(g({},a),{distance:this.calculateDistance(t,e,a.latitude,a.longitude)})).sort((a,i)=>a.distance-i.distance).slice(0,o)})}saveRoute(t){return c(this,null,function*(){try{let e=this.getStoredRoutes(),r=_(g({},t),{id:`${t.start_lat}_${t.start_lng}_${t.end_lat}_${t.end_lng}_${t.travel_mode}`,created_at:new Date().toISOString()}),o=e.filter(n=>n.id!==r.id);o.push(r);let s=o.slice(-100);localStorage.setItem(this.STORAGE_KEYS.ROUTES,JSON.stringify(s)),console.log("\u{1F4BE} Route saved to cache")}catch(e){console.error("\u274C Error saving route:",e)}})}getRoute(t,e,r,o,s){return c(this,null,function*(){try{let n=this.getStoredRoutes(),a=`${t}_${e}_${r}_${o}_${s}`,i=n.find(E=>E.id===a);if(i&&i.created_at){let E=Date.now()-new Date(i.created_at).getTime(),l=24*60*60*1e3;if(E<l)return i}return null}catch(n){return console.error("\u274C Error fetching route:",n),null}})}getStoredRoutes(){try{let t=localStorage.getItem(this.STORAGE_KEYS.ROUTES);return t?JSON.parse(t):[]}catch(t){return console.error("\u274C Error parsing stored routes:",t),[]}}saveMapTile(t,e,r,o){return c(this,null,function*(){try{let s=this.getStoredTiles(),n=`${t}_${e}_${r}`;s[n]={key:n,z:t,x:e,y:r,tile_data:o,created_at:new Date().toISOString()};let a=Object.keys(s);a.length>this.TILE_CACHE_LIMIT&&a.map(l=>({key:l,created_at:s[l].created_at})).sort((l,f)=>new Date(l.created_at).getTime()-new Date(f.created_at).getTime()).slice(0,a.length-this.TILE_CACHE_LIMIT).forEach(l=>delete s[l.key]),localStorage.setItem(this.STORAGE_KEYS.MAP_TILES,JSON.stringify(s))}catch(s){console.error("\u274C Error saving map tile:",s)}})}getMapTile(t,e,r){return c(this,null,function*(){try{let o=this.getStoredTiles(),s=`${t}_${e}_${r}`;return o[s]||null}catch(o){return console.error("\u274C Error getting map tile:",o),null}})}getStoredTiles(){try{let t=localStorage.getItem(this.STORAGE_KEYS.MAP_TILES);return t?JSON.parse(t):{}}catch(t){return console.error("\u274C Error parsing stored tiles:",t),{}}}setOfflineMode(t){localStorage.setItem(this.STORAGE_KEYS.OFFLINE_MODE,t.toString()),console.log(`\u{1F504} Offline mode ${t?"enabled":"disabled"}`)}isOfflineMode(){return localStorage.getItem(this.STORAGE_KEYS.OFFLINE_MODE)==="true"}saveUserLocation(t,e){let r={lat:t,lng:e,timestamp:new Date().toISOString()};localStorage.setItem(this.STORAGE_KEYS.USER_LOCATION,JSON.stringify(r))}getLastUserLocation(){try{let t=localStorage.getItem(this.STORAGE_KEYS.USER_LOCATION);return t?JSON.parse(t):null}catch(t){return console.error("\u274C Error getting user location:",t),null}}isDataAvailable(){return c(this,null,function*(){return(yield this.getEvacuationCenters()).length>0})}getLastSyncTime(){return localStorage.getItem(this.STORAGE_KEYS.LAST_SYNC)}calculateDistance(t,e,r,o){let n=this.toRadians(r-t),a=this.toRadians(o-e),i=Math.sin(n/2)*Math.sin(n/2)+Math.cos(this.toRadians(t))*Math.cos(this.toRadians(r))*Math.sin(a/2)*Math.sin(a/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))}toRadians(t){return t*(Math.PI/180)}clearOfflineData(){Object.values(this.STORAGE_KEYS).forEach(t=>{localStorage.removeItem(t)}),this.initializeStorage(),console.log("\u{1F5D1}\uFE0F All offline data cleared")}getStorageInfo(){let t=0;Object.values(this.STORAGE_KEYS).forEach(o=>{let s=localStorage.getItem(o);s&&(t+=new Blob([s]).size)});let e=this.MAX_STORAGE_SIZE-t,r=t/this.MAX_STORAGE_SIZE*100;return{used:t,available:e,percentage:r}}isOnline(){return navigator.onLine}static{this.\u0275fac=function(e){return new(e||S)(d(O))}}static{this.\u0275prov=u({token:S,factory:S.\u0275fac,providedIn:"root"})}}return S})();export{y as a};
