import{a as U}from"./chunk-FULEFYAM.js";import{$ as A,$a as _,A as u,Bb as h,C as w,Cb as L,D as I,Db as x,F as e,G as t,H as r,J as c,K as D,M as n,N as g,Na as C,O as z,Oa as y,V as F,W as G,Wa as f,X as b,ab as O,cb as v,ea as B,fb as P,ga as N,gb as E,oa as k,ub as M,vb as S,y as d,z as p,zb as j}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import{g as m}from"./chunk-2R6CW7ES.js";function W(o,T){if(o&1&&(e(0,"p"),n(1),t()),o&2){let i=D();d(),g(i.userData.email)}}var Y=".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:.9375rem;margin-bottom:15px}";function R(o,T){if(o&1&&(e(0,"div",7)(1,"div",8)(2,"span",9),n(3),t(),e(4,"span",10),n(5),t()()()),o&2){let i=T.$implicit;d(3),g(i.icon),d(2),g(i.label)}}var se=(()=>{class o{constructor(i,a,l,s,H){this.modalCtrl=i,this.alertCtrl=a,this.toastCtrl=l,this.http=s,this.router=H,this.userData={},this.loadUserData()}goToSettings(){this.router.navigate(["/settings"])}loadUserData(){let i=localStorage.getItem("userData");i&&(this.userData=JSON.parse(i))}openTermsModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:q,cssClass:"terms-modal"})).present()})}openPrivacyModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:K,cssClass:"terms-modal"})).present()})}openEmergencyContactsModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:Q,cssClass:"terms-modal"})).present()})}openSafetyTipsModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:V,cssClass:"terms-modal"})).present()})}openGuideModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:$,cssClass:"terms-modal"})).present()})}openAccountInfoModal(){return m(this,null,function*(){yield(yield this.modalCtrl.create({component:J,cssClass:"account-info-modal"})).present()})}testFCM(){return m(this,null,function*(){if(localStorage.getItem("google_play_services_missing")==="true"){yield(yield this.alertCtrl.create({header:"Google Play Services Required",message:"Push notifications require Google Play Services. Would you like to install or update Google Play Services?",buttons:[{text:"Install/Update",handler:()=>{window.open("market://details?id=com.google.android.gms","_system")}},{text:"Continue Anyway",handler:()=>{this.checkFCMToken()}}]})).present();return}yield this.checkFCMToken()})}checkFCMToken(){return m(this,null,function*(){let i=localStorage.getItem("fcm_token");if(!i){yield(yield this.alertCtrl.create({header:"No FCM Token",message:"No FCM token found. Please restart the app to generate a token.",buttons:["OK"]})).present();return}yield(yield this.alertCtrl.create({header:"FCM Token",message:`Current token: ${i.substring(0,20)}...`,buttons:[{text:"Test Local Notification",handler:()=>{this.showTestNotification()}},{text:"Send from Backend",handler:()=>{this.sendTestNotificationFromBackend(i)}},{text:"Check Google Play",handler:()=>{this.checkGooglePlayServices()}},{text:"Cancel",role:"cancel"}]})).present()})}checkGooglePlayServices(){return m(this,null,function*(){try{window.open("market://details?id=com.google.android.gms","_system")}catch(i){console.error("Error opening Google Play Store:",i),yield(yield this.alertCtrl.create({header:"Error",message:"Could not open Google Play Store. Please check if Google Play Store is installed on your device.",buttons:["OK"]})).present()}})}showTestNotification(){return m(this,null,function*(){let i={title:"Test Notification",body:"This is a local test notification",category:"General",severity:"medium",wasTapped:!1,time:new Date().toISOString()};"vibrate"in navigator&&navigator.vibrate([500,100,500]),yield(yield this.alertCtrl.create({header:i.title,subHeader:i.category?`${i.category.toUpperCase()}`:"",message:i.body,buttons:["OK"]})).present()})}sendTestNotificationFromBackend(i){return m(this,null,function*(){yield(yield this.toastCtrl.create({message:"Sending test notification from backend...",duration:2e3})).present(),this.http.post(`${U.apiUrl}/test-notification`,{token:i,title:"Test from App",message:"This is a test notification sent from the app",category:"General",severity:"medium"}).subscribe({next:()=>{this.toastCtrl.create({message:"Test notification sent successfully!",duration:3e3,color:"success"}).then(l=>l.present())},error:l=>{this.alertCtrl.create({header:"Error",message:`Failed to send test notification: ${l.message||JSON.stringify(l)}`,buttons:["OK"]}).then(s=>s.present())}})})}static{this.\u0275fac=function(a){return new(a||o)(p(h),p(j),p(L),p(A),p(B))}}static{this.\u0275cmp=u({type:o,selectors:[["app-profile"]],decls:38,vars:2,consts:[[1,"profile-header"],[1,"profile-background"],[1,"profile-avatar"],["name","person",1,"avatar-icon"],[1,"profile-info"],[4,"ngIf"],["lines","full",1,"menu-list"],["button","",2,"padding-top","10px",3,"click"],["src","assets/setting (1).png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],[2,"padding-left","15px","font-size","17px"],["src","assets/info.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/medical-call.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/first-aid-box.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/shield.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["src","assets/terms-and-conditions.png","slot","start",2,"width","28px","height","28px","display","block","margin","auto"],["name","settings-outline","slot","start",2,"width","28px","height","28px","display","block","margin","auto","color","#3880ff"]],template:function(a,l){a&1&&(e(0,"ion-content")(1,"div",0)(2,"div",1)(3,"div",2),r(4,"ion-icon",3),t(),e(5,"div",4)(6,"h2"),n(7),t(),w(8,W,2,1,"p",5),t()()(),e(9,"ion-list",6)(10,"ion-item",7),c("click",function(){return l.openAccountInfoModal()}),r(11,"img",8),e(12,"ion-label",9),n(13,"Account Information"),t()(),e(14,"ion-item",7),c("click",function(){return l.openGuideModal()}),r(15,"img",10),e(16,"ion-label",9),n(17,"Reference Guide for Map Symbols"),t()(),e(18,"ion-item",7),c("click",function(){return l.openEmergencyContactsModal()}),r(19,"img",11),e(20,"ion-label",9),n(21,"Emergency Contacts"),t()(),e(22,"ion-item",7),c("click",function(){return l.openSafetyTipsModal()}),r(23,"img",12),e(24,"ion-label",9),n(25,"Safety Tips"),t()(),e(26,"ion-item",7),c("click",function(){return l.openPrivacyModal()}),r(27,"img",13),e(28,"ion-label",9),n(29,"Privacy Policy"),t()(),e(30,"ion-item",7),c("click",function(){return l.openTermsModal()}),r(31,"img",14),e(32,"ion-label",9),n(33,"Terms and Condition"),t()(),e(34,"ion-item",7),c("click",function(){return l.goToSettings()}),r(35,"ion-icon",15),e(36,"ion-label",9),n(37,"Notification Settings"),t()()()()),a&2&&(d(7),z("Hi, ",l.userData.full_name||"User",""),d(),I("ngIf",l.userData.email))},dependencies:[x,f,O,v,P,E,b,G,k,N],styles:[`@charset "UTF-8";ion-header[_ngcontent-%COMP%]{background:var(--ion-color-primary)}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: transparent}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{color:#fff}ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-back-button[_ngcontent-%COMP%]{--color: white}.profile-header[_ngcontent-%COMP%]{position:relative;margin-bottom:20px}.profile-background[_ngcontent-%COMP%]{background:linear-gradient(135deg,#03b2dd,#0891b2);height:200px;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;overflow:hidden}.profile-background[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.05"><circle cx="30" cy="30" r="2"/></g></svg>');pointer-events:none}.profile-avatar[_ngcontent-%COMP%]{width:80px;height:80px;background:#fff3;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-bottom:16px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:3px solid rgba(255,255,255,.3);box-shadow:0 8px 32px #0000001a}.profile-avatar[_ngcontent-%COMP%]   .avatar-icon[_ngcontent-%COMP%]{font-size:40px;color:#fff}.profile-info[_ngcontent-%COMP%]{text-align:center;color:#fff}.profile-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0 0 8px;font-size:24px;font-weight:600;text-shadow:0 2px 4px rgba(0,0,0,.1)}.profile-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:16px;opacity:.9;font-weight:400}.menu-list[_ngcontent-%COMP%]{background:transparent;margin-top:20px;padding:0 16px}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]{--padding-start: 16px;--padding-end: 16px;--min-height: 60px;margin-bottom:8px;border-radius:12px;--background: white;box-shadow:0 2px 8px #0000001a;transition:all .3s ease}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:20px;margin-right:16px;color:var(--ion-color-primary)}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-size:16px;font-weight:500;color:var(--ion-color-dark)}.menu-list[_ngcontent-%COMP%]   ion-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:brightness(0) saturate(100%) invert(34%) sepia(77%) saturate(2476%) hue-rotate(203deg) brightness(99%) contrast(92%)}.terms-modal[_ngcontent-%COMP%]{--height: 90%;--border-radius: 16px}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]{--background: var(--ion-color-light)}.terms-modal[_ngcontent-%COMP%]   ion-header[_ngcontent-%COMP%]   ion-toolbar[_ngcontent-%COMP%]   ion-title[_ngcontent-%COMP%]{font-size:18px;font-weight:600}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:24px;font-weight:700;margin-bottom:8px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   .effective-date[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:14px;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:18px;font-weight:600;margin:24px 0 12px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:16px}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-size:16px;line-height:1.5;margin-bottom:8px;padding-left:24px;position:relative}.terms-modal[_ngcontent-%COMP%]   ion-content[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:before{content:"\\2022";position:absolute;left:8px;color:var(--ion-color-primary)}.terms-modal[_ngcontent-%COMP%]   .legend-title[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:20px;font-weight:600;margin-bottom:24px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]{margin-top:30px;display:flex;flex-direction:column;align-items:center}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:16px;background:var(--ion-color-light);border-radius:8px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:32px;height:32px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .icon-wrapper[_ngcontent-%COMP%]   .legend-icon[_ngcontent-%COMP%]{font-size:20px}.terms-modal[_ngcontent-%COMP%]   .legend-container[_ngcontent-%COMP%]   .legend-items[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%]{color:var(--ion-color-dark);font-size:16px}`]})}}return o})(),q=(()=>{class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:61,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"terms-content"],[1,"modal-section-title"],[1,"effective-date"],[1,"welcome"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),n(4,"Terms and Conditions"),t()(),e(5,"ion-buttons",1)(6,"ion-button",2),c("click",function(){return l.dismiss()}),n(7,"Close"),t()()()(),e(8,"ion-content",3)(9,"div",4)(10,"h1",5)(11,"strong"),n(12,"Terms and Conditions"),t()(),e(13,"p",6),n(14,"Effective Date: April 26, 2025"),t(),e(15,"p",7),n(16,'Welcome to Evacuation Mapping System ("we", "our", or "us"). These '),e(17,"strong"),n(18,"Terms and Conditions"),t(),n(19,' ("Terms") govern your access to and use of our online evacuation mapping system (the "Service"). By registering or using the Service, you agree to be bound by these Terms.'),t(),e(20,"section")(21,"h2",5),n(22,"1. User Eligibility"),t(),e(23,"p"),n(24,"To use this service, you must be at least 13 years old. By registering, you confirm that the information provided is accurate and complete."),t()(),e(25,"section")(26,"h2",5),n(27,"2. User Account"),t(),e(28,"p"),n(29,"To access certain features of the Service, you must create an account. You agree to provide:"),t(),e(30,"ul")(31,"li"),n(32,"Your full name"),t(),e(33,"li"),n(34,"A valid email address"),t(),e(35,"li"),n(36,"A password"),t(),e(37,"li"),n(38,"Your location data (for accurate evacuation mapping)"),t()(),e(39,"p"),n(40,"You are responsible for maintaining the confidentiality of your account and for all activities that occur under your account."),t()(),e(41,"section")(42,"h2",5),n(43,"3. Use of Service"),t(),e(44,"p"),n(45,"You agree to use the system solely for lawful purposes and in a way that does not infringe the rights of others. Misuse of the system, including providing false information or tampering with the mapping process, may result in suspension or termination of your account."),t()(),e(46,"section")(47,"h2",5),n(48,"4. Modifications"),t(),e(49,"p"),n(50,"We reserve the right to modify or discontinue the Service at any time without notice. Continued use of the Service following changes means you accept those changes."),t()(),e(51,"section")(52,"h2",5),n(53,"5. Limitation of Liability"),t(),e(54,"p"),n(55,"We strive to provide accurate evacuation data but do not guarantee the completeness, accuracy, or timeliness of the information provided. We are not liable for any loss or damage arising from the use or inability to use the Service."),t()(),e(56,"section")(57,"h2",5),n(58,"6. Termination"),t(),e(59,"p"),n(60,"We may suspend or terminate your access to the Service if you violate these Terms."),t()()()())},dependencies:[x,C,y,f,_,M,S],styles:[Y]})}}return o})(),K=(()=>{class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:66,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"effective-date"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0)(3,"strong"),n(4,"Privacy Policy"),t()(),e(5,"ion-buttons",1)(6,"ion-button",2),c("click",function(){return l.dismiss()}),n(7,"Close"),t()()()(),e(8,"ion-content",3)(9,"h2",4)(10,"strong"),n(11,"Privacy Policy"),t()(),e(12,"p",5),n(13,"Effective Date: April 26, 2025"),t(),e(14,"p"),n(15,"DisasterGuard is committed to protecting your privacy. This "),e(16,"strong"),n(17,"Privacy Policy"),t(),n(18," outlines how we collect, use, and protect your information when you use our evacuation mapping system."),t(),e(19,"h3",4),n(20,"1. Information We Collect"),t(),e(21,"p"),n(22,"We collect the following personal information upon registration:"),t(),e(23,"ul")(24,"li"),n(25,"Name"),t(),e(26,"li"),n(27,"Email address"),t(),e(28,"li"),n(29,"Password (stored securely)"),t(),e(30,"li"),n(31,"Location data (for evacuation mapping purposes)"),t()(),e(32,"h3",4),n(33,"2. How We Use Your Information"),t(),e(34,"p"),n(35,"Your data is used solely to:"),t(),e(36,"ul")(37,"li"),n(38,"Provide personalized evacuation routes and mapping"),t(),e(39,"li"),n(40,"Contact you regarding urgent updates or emergencies"),t(),e(41,"li"),n(42,"Improve system functionality"),t()(),e(43,"p"),n(44,"We do not sell, rent, or share your personal information with third parties, except as required by law or to ensure user safety during emergencies."),t(),e(45,"h3",4),n(46,"3. Data Security"),t(),e(47,"p"),n(48,"We implement appropriate security measures to protect your data. Your password is encrypted, and location data is only used to provide real-time evacuation support."),t(),e(49,"h3",4),n(50,"4. Your Rights"),t(),e(51,"p"),n(52,"You may:"),t(),e(53,"ul")(54,"li"),n(55,"Access or update your personal data"),t(),e(56,"li"),n(57,"Request deletion of your account"),t(),e(58,"li"),n(59,"Opt-out of communications at any time"),t()(),e(60,"p"),n(61,"To do so, contact us at: <EMAIL>"),t(),e(62,"h3",4),n(63,"5. Changes to This Policy"),t(),e(64,"p"),n(65,"We may update this Privacy Policy occasionally. You will be notified of any significant changes."),t()())},dependencies:[x,C,y,f,_,M,S],styles:[Y]})}}return o})(),$=(()=>{class o{constructor(i){this.modalCtrl=i,this.legendItems=[{icon:"\u{1F7E2}",label:"Your Location"},{icon:"\u{1F7E1}",label:"for Earthquake"},{icon:"\u26AB",label:"for Typhoon"},{icon:"\u{1F535}",label:"for Flash flood"}]}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:13,vars:1,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],[1,"modal-section-title"],[1,"legend-items"],["class","legend-item",4,"ngFor","ngForOf"],[1,"legend-item"],[1,"legend-icon-container"],[1,"legend-icon"],[1,"legend-label"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Map Symbols Guide"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),c("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"h3",4)(9,"strong"),n(10,"Reference Guide for Map Symbols"),t()(),e(11,"div",5),w(12,R,6,2,"div",6),t()()),a&2&&(d(12),I("ngForOf",l.legendItems))},dependencies:[x,C,y,f,_,M,S,b,F],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}.modal-section-title[_ngcontent-%COMP%]{font-size:.9375rem;margin-bottom:15px}.legend-items[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:10px}.legend-icon-container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:15px}.legend-icon[_ngcontent-%COMP%]{font-size:24px;width:30px;text-align:center}.legend-label[_ngcontent-%COMP%]{flex-grow:1}"]})}}return o})(),J=(()=>{class o{constructor(i){this.modalCtrl=i,this.userData={},this.loadUserData()}loadUserData(){let i=localStorage.getItem("userData");i&&(this.userData=JSON.parse(i))}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:44,vars:5,consts:[["slot","end"],[3,"click"],[1,"ion-padding"],["name","person-outline","slot","start"],["name","call-outline","slot","start"],["name","calendar-outline","slot","start"],["name","male-female-outline","slot","start"],["name","location-outline","slot","start"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),n(3,"Account Information"),t(),e(4,"ion-buttons",0)(5,"ion-button",1),c("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",2)(8,"ion-list")(9,"ion-item"),r(10,"ion-icon",3),e(11,"ion-label")(12,"h2"),n(13,"Full Name"),t(),e(14,"p"),n(15),t()()(),e(16,"ion-item"),r(17,"ion-icon",4),e(18,"ion-label")(19,"h2"),n(20,"Contact Number"),t(),e(21,"p"),n(22),t()()(),e(23,"ion-item"),r(24,"ion-icon",5),e(25,"ion-label")(26,"h2"),n(27,"Age"),t(),e(28,"p"),n(29),t()()(),e(30,"ion-item"),r(31,"ion-icon",6),e(32,"ion-label")(33,"h2"),n(34,"Gender"),t(),e(35,"p"),n(36),t()()(),e(37,"ion-item"),r(38,"ion-icon",7),e(39,"ion-label")(40,"h2"),n(41,"Address"),t(),e(42,"p"),n(43),t()()()()()),a&2&&(d(15),g(l.userData.full_name),d(7),g(l.userData.mobile_number),d(7),g(l.userData.age),d(7),g(l.userData.gender),d(7),g(l.userData.address))},dependencies:[x,C,y,f,_,O,v,P,E,M,S,b,k],encapsulation:2})}}return o})(),Q=(()=>{class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:44,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"],["name","call-outline","slot","start"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Emergency Contacts"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),c("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"ion-list")(9,"ion-item"),r(10,"ion-icon",4),e(11,"ion-label")(12,"h2"),n(13,"National Emergency Hotline"),t(),e(14,"p"),n(15,"911"),t()()(),e(16,"ion-item"),r(17,"ion-icon",4),e(18,"ion-label")(19,"h2"),n(20,"Fire Department"),t(),e(21,"p"),n(22,"160"),t()()(),e(23,"ion-item"),r(24,"ion-icon",4),e(25,"ion-label")(26,"h2"),n(27,"Police"),t(),e(28,"p"),n(29,"117"),t()()(),e(30,"ion-item"),r(31,"ion-icon",4),e(32,"ion-label")(33,"h2"),n(34,"Red Cross"),t(),e(35,"p"),n(36,"143"),t()()(),e(37,"ion-item"),r(38,"ion-icon",4),e(39,"ion-label")(40,"h2"),n(41,"Local Disaster Office"),t(),e(42,"p"),n(43,"Contact your LGU"),t()()()()())},dependencies:[x,C,y,f,_,O,v,P,E,M,S,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}h2[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:4px}p[_ngcontent-%COMP%]{font-size:.95rem;color:var(--ion-color-medium)}"]})}}return o})(),V=(()=>{class o{constructor(i){this.modalCtrl=i}dismiss(){this.modalCtrl.dismiss()}static{this.\u0275fac=function(a){return new(a||o)(p(h))}}static{this.\u0275cmp=u({type:o,selectors:[["ng-component"]],decls:49,vars:0,consts:[[1,"modal-title"],["slot","end"],[3,"click"],[1,"ion-padding"]],template:function(a,l){a&1&&(e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title",0),n(3,"Safety Tips"),t(),e(4,"ion-buttons",1)(5,"ion-button",2),c("click",function(){return l.dismiss()}),n(6,"Close"),t()()()(),e(7,"ion-content",3)(8,"ion-list")(9,"ion-item")(10,"ion-label")(11,"h2"),n(12,"Earthquake"),t(),e(13,"ul")(14,"li"),n(15,"Drop, Cover, and Hold On."),t(),e(16,"li"),n(17,"Stay away from windows and heavy objects."),t(),e(18,"li"),n(19,"Evacuate only when safe."),t()()()(),e(20,"ion-item")(21,"ion-label")(22,"h2"),n(23,"Flood"),t(),e(24,"ul")(25,"li"),n(26,"Move to higher ground immediately."),t(),e(27,"li"),n(28,"Avoid walking or driving through floodwaters."),t()()()(),e(29,"ion-item")(30,"ion-label")(31,"h2"),n(32,"Typhoon"),t(),e(33,"ul")(34,"li"),n(35,"Stay indoors and away from glass windows."),t(),e(36,"li"),n(37,"Prepare an emergency kit."),t()()()(),e(38,"ion-item")(39,"ion-label")(40,"h2"),n(41,"General"),t(),e(42,"ul")(43,"li"),n(44,"Keep emergency contacts accessible."),t(),e(45,"li"),n(46,"Prepare a Go Bag with essentials."),t(),e(47,"li"),n(48,"Stay informed via official channels."),t()()()()()())},dependencies:[x,C,y,f,_,v,P,E,M,S,b],styles:[".modal-title[_ngcontent-%COMP%]{font-size:1.2rem;font-weight:700}h2[_ngcontent-%COMP%]{font-size:1rem;margin-bottom:4px}ul[_ngcontent-%COMP%]{margin:0;padding-left:18px;font-size:.95rem;color:var(--ion-color-medium)}li[_ngcontent-%COMP%]{margin-bottom:4px}"]})}}return o})();export{J as AccountInfoModalComponent,Q as EmergencyContactsModalComponent,$ as GuideModalComponent,K as PrivacyModalComponent,se as ProfilePage,V as SafetyTipsModalComponent,q as TermsModalComponent};
