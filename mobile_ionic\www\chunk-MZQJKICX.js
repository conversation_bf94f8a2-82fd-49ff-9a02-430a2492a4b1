import{a as ae}from"./chunk-FULEFYAM.js";import{$ as O,$a as J,A as P,C as x,D as M,Db as re,F as e,G as t,Ga as B,H as y,Ha as q,I as w,Ia as F,J as p,Ja as j,K as _,M as n,N as D,Na as R,O as E,Oa as H,Q as u,R as h,S as f,Ua as G,W as v,Wa as Y,X as T,ab as K,bb as Q,cb as X,ea as I,fb as Z,ha as V,ia as k,ja as W,ka as A,la as N,m as C,ma as z,na as U,o as S,oa as L,ob as $,pb as ee,q as l,r as m,tb as te,ub as ne,vb as ie,y as d,yb as oe,z as b}from"./chunk-QCXYQNJC.js";import"./chunk-6WVAEWPV.js";import"./chunk-HYNAH5QB.js";import"./chunk-5AIHQZWU.js";import"./chunk-4PQ5B4D2.js";import"./chunk-HC6MZPB3.js";import"./chunk-SV2ZKNWA.js";import"./chunk-EPGIQT2W.js";import"./chunk-RS5W3JWO.js";import"./chunk-DY4KE6AI.js";import"./chunk-GIGBYVJT.js";import"./chunk-KGEDUKSE.js";import"./chunk-MCRJI3T3.js";import"./chunk-OBBPMR2I.js";import"./chunk-AMQPVFGX.js";import"./chunk-KKCAABTQ.js";import"./chunk-OFX7WKKZ.js";import"./chunk-F4H6ZFEG.js";import"./chunk-NMYJD6OP.js";import"./chunk-XXJXE6HG.js";import"./chunk-SV7S5NYR.js";import"./chunk-WTCPO44B.js";import"./chunk-QVY4QQUF.js";import"./chunk-2HRRFJKF.js";import"./chunk-UYQ7EZNZ.js";import"./chunk-BAKMWPBW.js";import"./chunk-7D6K5XYM.js";import"./chunk-OBXDPQ3V.js";import"./chunk-2R6CW7ES.js";var se=(()=>{class s{constructor(r){this.http=r,this.apiUrl=`${ae.apiUrl}/mobile-users`}saveUserData(r){return this.http.post(this.apiUrl,r)}createUser(r){return this.http.post(this.apiUrl,r)}static{this.\u0275fac=function(o){return new(o||s)(S(O))}}static{this.\u0275prov=C({token:s,factory:s.\u0275fac,providedIn:"root"})}}return s})();function ce(s,le){if(s&1&&(e(0,"ion-text",25)(1,"p"),n(2),t()()),s&2){let r=_();d(2),D(r.errorMessage)}}function ge(s,le){if(s&1){let r=w();e(0,"ion-header")(1,"ion-toolbar")(2,"ion-title"),n(3,"Terms and Conditions"),t(),e(4,"ion-buttons",26)(5,"ion-button",27),p("click",function(){l(r);let i=_();return m(i.closeTermsModal())}),y(6,"ion-icon",28),t()()()(),e(7,"ion-content",1)(8,"div",29)(9,"h2"),n(10,"Alerto - Emergency Evacuation App"),t(),e(11,"h3"),n(12,"Terms and Conditions of Use"),t(),e(13,"p")(14,"strong"),n(15,"Last Updated:"),t(),n(16),t(),e(17,"h4"),n(18,"1. Acceptance of Terms"),t(),e(19,"p"),n(20,"By using the Alerto emergency evacuation application, you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use this application."),t(),e(21,"h4"),n(22,"2. Purpose of the Application"),t(),e(23,"p"),n(24,"Alerto is designed to provide emergency evacuation information and guidance during natural disasters including earthquakes, typhoons, and flash floods in the Philippines. The app provides location-based evacuation center recommendations and emergency notifications."),t(),e(25,"h4"),n(26,"3. User Responsibilities"),t(),e(27,"p"),n(28,"\u2022 Provide accurate personal information including contact details"),t(),e(29,"p"),n(30,"\u2022 Keep your mobile number updated for emergency notifications"),t(),e(31,"p"),n(32,"\u2022 Use the app responsibly during emergency situations"),t(),e(33,"p"),n(34,"\u2022 Follow official emergency protocols and local authority instructions"),t(),e(35,"h4"),n(36,"4. Data Collection and Privacy"),t(),e(37,"p"),n(38,"We collect and store your personal information including name, mobile number, age, gender, and address to provide personalized emergency services. Your location data may be accessed to provide relevant evacuation center recommendations."),t(),e(39,"h4"),n(40,"5. Emergency Notifications"),t(),e(41,"p"),n(42,"By using this app, you consent to receive emergency push notifications on your device. These notifications are critical for your safety during disaster events."),t(),e(43,"h4"),n(44,"6. Limitation of Liability"),t(),e(45,"p"),n(46,"While we strive to provide accurate and timely information, Alerto and its developers are not liable for any damages or losses resulting from the use of this application. Always follow official emergency protocols and local authority guidance."),t(),e(47,"h4"),n(48,"7. Service Availability"),t(),e(49,"p"),n(50,"We cannot guarantee uninterrupted service availability, especially during extreme weather conditions or network outages that may occur during disasters."),t(),e(51,"h4"),n(52,"8. Updates and Modifications"),t(),e(53,"p"),n(54,"These terms may be updated periodically. Continued use of the application constitutes acceptance of any modifications."),t(),e(55,"h4"),n(56,"9. Contact Information"),t(),e(57,"p"),n(58,"For questions about these terms or the application, please contact our support team through the app's feedback feature."),t(),e(59,"p")(60,"strong"),n(61,'By clicking "I accept" below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.'),t()()(),e(62,"div",30)(63,"ion-button",31),p("click",function(){l(r);let i=_();return m(i.closeTermsModal())}),n(64," Close "),t(),e(65,"ion-button",32),p("click",function(){l(r);let i=_();return m(i.acceptTerms())}),n(66," I Accept "),t()()()}if(s&2){let r=_();d(16),E(" ",r.getCurrentDate(),"")}}var we=(()=>{class s{constructor(r,o){this.router=r,this.mobileUserService=o,this.userData={full_name:"",mobile_number:"",age:"",gender:"",address:""},this.acceptedTerms=!1,this.showError=!1,this.errorMessage="",this.isTermsModalOpen=!1}openTermsModal(){this.isTermsModalOpen=!0}closeTermsModal(){this.isTermsModalOpen=!1}acceptTerms(){this.acceptedTerms=!0,this.closeTermsModal()}getCurrentDate(){return new Date().toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}onMobileNumberInput(r){let o=r.target.value;o=o.replace(/\D/g,""),o.length>10&&(o=o.substring(0,10)),o.length>0&&!o.startsWith("9")&&o.length===1&&o!=="9"&&(o="9"+o),this.userData.mobile_number=o,this.showError&&this.errorMessage.includes("Mobile number")&&(this.showError=!1,this.errorMessage="")}getCompletePhoneNumber(){return this.userData.mobile_number?`+63${this.userData.mobile_number}`:""}isValidPhilippineMobile(){let r=this.userData.mobile_number;return/^9[0-9]{9}$/.test(r)&&r.length===10}onSave(){console.log(this.userData);let r=["full_name","mobile_number","age","gender","address"];for(let o of r){let i=this.userData[o];if(i==null||i.toString().trim()===""){this.showError=!0,this.errorMessage="All fields are required.";return}}if(!this.isValidPhilippineMobile()){this.showError=!0,this.errorMessage="Mobile number must be exactly 10 digits starting with 9 (e.g., +63-************).";return}if(isNaN(Number(this.userData.age))||Number(this.userData.age)<=0){this.showError=!0,this.errorMessage="Age must be a positive number.";return}if(!this.acceptedTerms){this.showError=!0,this.errorMessage="You must accept the Terms and Conditions.";return}this.mobileUserService.createUser({full_name:this.userData.full_name,mobile_number:this.userData.mobile_number,age:Number(this.userData.age),gender:this.userData.gender,address:this.userData.address}).subscribe({next:()=>{let o={full_name:this.userData.full_name,mobile_number:this.userData.mobile_number,age:this.userData.age,gender:this.userData.gender,address:this.userData.address};localStorage.setItem("userData",JSON.stringify(o)),localStorage.setItem("onboardingComplete","true"),this.router.navigate(["/tabs/home"])},error:o=>{this.showError=!0,this.errorMessage=o.error?.message||"Failed to save user."}})}static{this.\u0275fac=function(o){return new(o||s)(b(I),b(se))}}static{this.\u0275cmp=P({type:s,selectors:[["app-data"]],decls:48,vars:8,consts:[["termsModal",""],[1,"ion-padding"],[1,"profile-container"],[1,"header-logo"],[1,"home-title"],["src","assets/ALERTO.png","alt","App Logo",1,"home-logo"],[3,"ngSubmit"],["position","floating",2,"font-size","15px"],["type","text","name","full_name","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],[2,"display","flex","align-items","center","width","100%"],[2,"font-size","20px","font-weight","bold","margin-right","8px","color","#333","min-width","40px"],["type","tel","name","mobile_number","placeholder","************","maxlength","10","required","",2,"font-size","20px","padding-bottom","10px","flex","1",3,"ngModelChange","ionInput","ngModel"],["type","number","name","age","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["position","floating"],["name","gender","required","",3,"ngModelChange","ngModel"],["value","Male"],["value","Female"],["value","Other"],["type","text","name","address","required","",2,"font-size","20px","padding-bottom","10px",3,"ngModelChange","ngModel"],["color","danger","class","error-message",4,"ngIf"],[1,"terms-checkbox"],["slot","start","name","acceptedTerms","required","",3,"ngModelChange","ngModel"],[2,"color","#3880ff","text-decoration","underline","cursor","pointer",3,"click"],["expand","block","type","submit",1,"ion-margin-top"],[3,"willDismiss","isOpen"],["color","danger",1,"error-message"],["slot","end"],[3,"click"],["name","close"],[1,"terms-content"],[1,"modal-buttons"],["expand","block","fill","outline",3,"click"],["expand","block",3,"click"]],template:function(o,i){if(o&1){let c=w();e(0,"ion-content",1)(1,"div",2)(2,"div",3)(3,"div",4),y(4,"img",5),n(5," Hi, Welcome to Safe Area!"),t()(),e(6,"form",6),p("ngSubmit",function(){return l(c),m(i.onSave())}),e(7,"ion-item")(8,"ion-label",7),n(9,"Full Name:"),t(),e(10,"ion-input",8),f("ngModelChange",function(a){return l(c),h(i.userData.full_name,a)||(i.userData.full_name=a),m(a)}),t()(),e(11,"ion-item")(12,"ion-label",7),n(13,"Mobile Number:"),t(),e(14,"div",9)(15,"span",10),n(16,"+63"),t(),e(17,"ion-input",11),f("ngModelChange",function(a){return l(c),h(i.userData.mobile_number,a)||(i.userData.mobile_number=a),m(a)}),p("ionInput",function(a){return l(c),m(i.onMobileNumberInput(a))}),t()()(),e(18,"ion-item")(19,"ion-label",7),n(20,"Age:"),t(),e(21,"ion-input",12),f("ngModelChange",function(a){return l(c),h(i.userData.age,a)||(i.userData.age=a),m(a)}),t()(),e(22,"ion-item")(23,"ion-label",13),n(24,"Gender "),e(25,"ion-select",14),f("ngModelChange",function(a){return l(c),h(i.userData.gender,a)||(i.userData.gender=a),m(a)}),e(26,"ion-select-option",15),n(27,"Male"),t(),e(28,"ion-select-option",16),n(29,"Female"),t(),e(30,"ion-select-option",17),n(31,"Other"),t()()()(),e(32,"ion-item")(33,"ion-label",7),n(34,"Address:"),t(),e(35,"ion-input",18),f("ngModelChange",function(a){return l(c),h(i.userData.address,a)||(i.userData.address=a),m(a)}),t()(),x(36,ce,3,1,"ion-text",19),e(37,"div",20)(38,"ion-checkbox",21),f("ngModelChange",function(a){return l(c),h(i.acceptedTerms,a)||(i.acceptedTerms=a),m(a)}),t(),e(39,"label"),n(40,"I accept the "),e(41,"a",22),p("click",function(){return l(c),m(i.openTermsModal())}),n(42,"Terms and Conditions"),t()()(),e(43,"ion-button",23),n(44," Save "),t()()()(),e(45,"ion-modal",24,0),p("willDismiss",function(){return l(c),m(i.closeTermsModal())}),x(47,ge,67,1,"ng-template"),t()}o&2&&(d(10),u("ngModel",i.userData.full_name),d(7),u("ngModel",i.userData.mobile_number),d(4),u("ngModel",i.userData.age),d(4),u("ngModel",i.userData.gender),d(10),u("ngModel",i.userData.address),d(),M("ngIf",i.showError),d(2),u("ngModel",i.acceptedTerms),d(7),M("isOpen",i.isTermsModalOpen))},dependencies:[re,R,H,G,Y,J,K,Q,X,Z,$,ee,te,ne,ie,oe,B,q,F,j,T,v,L,N,V,k,z,U,A,W],styles:[".profile-container[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;max-width:420px;margin:0 auto;padding:32px 20px}form[_ngcontent-%COMP%]{width:100%}ion-item[_ngcontent-%COMP%]{--background: #f9f9f9;--border-radius: 25px;--padding-start: 10px;--padding-end: 10px;--inner-padding-top: 5px;--inner-padding-bottom: 5px;margin-bottom:10px;box-shadow:0 1px 4px #0000000d;height:65px}ion-label[_ngcontent-%COMP%]{font-size:5px;color:#333}ion-input[_ngcontent-%COMP%], ion-select[_ngcontent-%COMP%]{font-size:15px}.terms-checkbox[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:12px;font-size:14px;color:#444;flex-wrap:wrap;line-height:1.4}.terms-checkbox[_ngcontent-%COMP%]   ion-checkbox[_ngcontent-%COMP%]{margin-right:8px}.terms-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{color:#03b2dd;text-align:center;margin-bottom:10px;font-size:1.5rem}.terms-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{color:#333;text-align:center;margin-bottom:20px;font-size:1.2rem}.terms-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#03b2dd;margin-top:20px;margin-bottom:10px;font-size:1.1rem;font-weight:600}.terms-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#444;line-height:1.6;margin-bottom:12px;text-align:justify}.terms-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{font-weight:600;color:#333;text-align:center;margin-top:20px;padding:15px;background-color:#f0f9ff;border-radius:8px;border-left:4px solid #03b2dd}.modal-buttons[_ngcontent-%COMP%]{margin-top:30px;padding:20px 0}.modal-buttons[_ngcontent-%COMP%]   ion-button[_ngcontent-%COMP%]{margin-bottom:10px;--border-radius: 25px;height:45px;font-weight:600}.modal-buttons[_ngcontent-%COMP%]   ion-button[fill=outline][_ngcontent-%COMP%]{--color: #666;--border-color: #666}.terms-checkbox[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#1565c0;text-decoration:underline;font-weight:500;margin-left:4px}ion-button[_ngcontent-%COMP%]{margin-top:24px;--background: #1565c0;--border-radius: 10px;--box-shadow: 0px 4px 10px rgba(0, 0, 0, .15)}.error-message[_ngcontent-%COMP%]{text-align:center;margin:10px 0;color:#e53935}.error-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;font-size:14px}.header-logo[_ngcontent-%COMP%]{text-align:center}.header-logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px;height:auto}.header-logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:18px;color:#222;font-weight:600;text-shadow:0px 2px 4px rgba(0,0,0,.15)}.home-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:15px;font-weight:700;letter-spacing:1px;text-shadow:1px 2px 4px #ccc;padding-top:59px}"]})}}return s})();export{we as DataPage};
