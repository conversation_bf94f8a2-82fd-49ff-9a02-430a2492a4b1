import{b as Gc}from"./chunk-SV2ZKNWA.js";import{c as Bc,e as Uc,f as Hc,g as $c,h as zc}from"./chunk-EPGIQT2W.js";import{b as Oc,c as kc,d as Fc,e as Pc,f as Lc}from"./chunk-KKCAABTQ.js";import{f as Bp}from"./chunk-OFX7WKKZ.js";import{a as hn,b as Up,c as Hp,d as $p,f as Nc}from"./chunk-F4H6ZFEG.js";import{a as jc}from"./chunk-NMYJD6OP.js";import{a as zp,d as Gp}from"./chunk-XXJXE6HG.js";import{b as zt}from"./chunk-SV7S5NYR.js";import{a as Wn,b as Rc,c as Si}from"./chunk-WTCPO44B.js";import{c as Vc}from"./chunk-QVY4QQUF.js";import{m as Wp}from"./chunk-2HRRFJKF.js";import{a as I,b as P,d as Ac,g as ge}from"./chunk-2R6CW7ES.js";function Gt(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var et=Gt(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function k(e){return typeof e=="function"}var Ti=Gt(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function pn(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var le=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(k(r))try{r()}catch(i){n=i instanceof Ti?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{qp(i)}catch(s){n=n??[],s instanceof Ti?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Ti(n)}}add(n){var t;if(n&&n!==this)if(this.closed)qp(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&pn(t,n)}remove(n){let{_finalizers:t}=this;t&&pn(t,n),n instanceof e&&n._removeParent(this)}};le.EMPTY=(()=>{let e=new le;return e.closed=!0,e})();var Wc=le.EMPTY;function xi(e){return e instanceof le||e&&"closed"in e&&k(e.remove)&&k(e.add)&&k(e.unsubscribe)}function qp(e){k(e)?e():e.unsubscribe()}var tt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var qn={setTimeout(e,n,...t){let{delegate:r}=qn;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=qn;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Ai(e){qn.setTimeout(()=>{let{onUnhandledError:n}=tt;if(n)n(e);else throw e})}function Jr(){}var Zp=qc("C",void 0,void 0);function Yp(e){return qc("E",void 0,e)}function Qp(e){return qc("N",e,void 0)}function qc(e,n,t){return{kind:e,value:n,error:t}}var gn=null;function Zn(e){if(tt.useDeprecatedSynchronousErrorHandling){let n=!gn;if(n&&(gn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=gn;if(gn=null,t)throw r}}else e()}function Kp(e){tt.useDeprecatedSynchronousErrorHandling&&gn&&(gn.errorThrown=!0,gn.error=e)}var mn=class extends le{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,xi(n)&&n.add(this)):this.destination=t0}static create(n,t,r){return new Wt(n,t,r)}next(n){this.isStopped?Yc(Qp(n),this):this._next(n)}error(n){this.isStopped?Yc(Yp(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Yc(Zp,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},JC=Function.prototype.bind;function Zc(e,n){return JC.call(e,n)}var Qc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){Ri(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){Ri(r)}else Ri(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){Ri(t)}}},Wt=class extends mn{constructor(n,t,r){super();let o;if(k(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&tt.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&Zc(n.next,i),error:n.error&&Zc(n.error,i),complete:n.complete&&Zc(n.complete,i)}):o=n}this.destination=new Qc(o)}};function Ri(e){tt.useDeprecatedSynchronousErrorHandling?Kp(e):Ai(e)}function e0(e){throw e}function Yc(e,n){let{onStoppedNotification:t}=tt;t&&qn.setTimeout(()=>t(e,n))}var t0={closed:!0,next:Jr,error:e0,complete:Jr};function n0(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=new Wt({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{t?r(n.defaultValue):o(new et)}});e.subscribe(i)})}var Ni=class extends le{constructor(n,t){super()}schedule(n,t=0){return this}};var eo={setInterval(e,n,...t){let{delegate:r}=eo;return r?.setInterval?r.setInterval(e,n,...t):setInterval(e,n,...t)},clearInterval(e){let{delegate:n}=eo;return(n?.clearInterval||clearInterval)(e)},delegate:void 0};var Oi=class extends Ni{constructor(n,t){super(n,t),this.scheduler=n,this.work=t,this.pending=!1}schedule(n,t=0){var r;if(this.closed)return this;this.state=n;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,t)),this.pending=!0,this.delay=t,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,t),this}requestAsyncId(n,t,r=0){return eo.setInterval(n.flush.bind(n,this),r)}recycleAsyncId(n,t,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return t;t!=null&&eo.clearInterval(t)}execute(n,t){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(n,t);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(n,t){let r=!1,o;try{this.work(n)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:n,scheduler:t}=this,{actions:r}=t;this.work=this.state=this.scheduler=null,this.pending=!1,pn(r,this),n!=null&&(this.id=this.recycleAsyncId(t,n,null)),this.delay=null,super.unsubscribe()}}};var Kc={now(){return(Kc.delegate||Date).now()},delegate:void 0};var Yn=class e{constructor(n,t=e.now){this.schedulerActionCtor=n,this.now=t}schedule(n,t=0,r){return new this.schedulerActionCtor(this,n).schedule(r,t)}};Yn.now=Kc.now;var ki=class extends Yn{constructor(n,t=Yn.now){super(n,t),this.actions=[],this._active=!1}flush(n){let{actions:t}=this;if(this._active){t.push(n);return}let r;this._active=!0;do if(r=n.execute(n.state,n.delay))break;while(n=t.shift());if(this._active=!1,r){for(;n=t.shift();)n.unsubscribe();throw r}}};var to=new ki(Oi),Xp=to;var Qn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Ie(e){return e}function Xc(...e){return Jc(e)}function Jc(e){return e.length===0?Ie:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var W=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=o0(t)?t:new Wt(t,r,o);return Zn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=Jp(r),new r((o,i)=>{let s=new Wt({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Qn](){return this}pipe(...t){return Jc(t)(this)}toPromise(t){return t=Jp(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function Jp(e){var n;return(n=e??tt.Promise)!==null&&n!==void 0?n:Promise}function r0(e){return e&&k(e.next)&&k(e.error)&&k(e.complete)}function o0(e){return e&&e instanceof mn||r0(e)&&xi(e)}function Fi(e){return e&&k(e.schedule)}function Pi(e){return e instanceof Date&&!isNaN(e)}function Li(e=0,n,t=Xp){let r=-1;return n!=null&&(Fi(n)?t=n:r=n),new W(o=>{let i=Pi(e)?+e-t.now():e;i<0&&(i=0);let s=0;return t.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function i0(e=0,n=to){return e<0&&(e=0),Li(e,e,n)}function el(e){return k(e?.lift)}function B(e){return n=>{if(el(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function j(e,n,t,r,o){return new tl(e,n,t,r,o)}var tl=class extends mn{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function Kn(){return B((e,n)=>{let t=null;e._refCount++;let r=j(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Xn=class extends W{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,el(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new le;let t=this.getSubject();n.add(this.source.subscribe(j(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=le.EMPTY)}return n}refCount(){return Kn()(this)}};var eg=Gt(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var X=(()=>{class e extends W{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new ji(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new eg}next(t){Zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){Zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){Zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Wc:(this.currentObservers=null,i.push(t),new le(()=>{this.currentObservers=null,pn(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new W;return t.source=this,t}}return e.create=(n,t)=>new ji(n,t),e})(),ji=class extends X{constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Wc}};var de=class extends X{constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var Re=new W(e=>e.complete());function tg(e){return e[e.length-1]}function Vi(e){return k(tg(e))?e.pop():void 0}function qt(e){return Fi(tg(e))?e.pop():void 0}var nl=function(e,n){return nl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])},nl(e,n)};function LF(e,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");nl(e,n);function t(){this.constructor=e}e.prototype=n===null?Object.create(n):(t.prototype=n.prototype,new t)}function w(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function rg(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(d){try{l(r.next(d))}catch(h){s(h)}}function c(d){try{l(r.throw(d))}catch(h){s(h)}}function l(d){d.done?i(d.value):o(d.value).then(a,c)}l((r=r.apply(e,n||[])).next())})}function ng(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function vn(e){return this instanceof vn?(this.v=e,this):new vn(e)}function og(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(y){return Promise.resolve(y).then(f,h)}}function a(f,y){r[f]&&(o[f]=function(S){return new Promise(function(N,z){i.push([f,S,N,z])>1||c(f,S)})},y&&(o[f]=y(o[f])))}function c(f,y){try{l(r[f](y))}catch(S){p(i[0][3],S)}}function l(f){f.value instanceof vn?Promise.resolve(f.value.v).then(d,h):p(i[0][2],f)}function d(f){c("next",f)}function h(f){c("throw",f)}function p(f,y){f(y),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ig(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof ng=="function"?ng(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Jn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Bi(e){return k(e?.then)}function Ui(e){return k(e[Qn])}function Hi(e){return Symbol.asyncIterator&&k(e?.[Symbol.asyncIterator])}function $i(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function s0(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var zi=s0();function Gi(e){return k(e?.[zi])}function Wi(e){return og(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield vn(t.read());if(o)return yield vn(void 0);yield yield vn(r)}}finally{t.releaseLock()}})}function qi(e){return k(e?.getReader)}function te(e){if(e instanceof W)return e;if(e!=null){if(Ui(e))return a0(e);if(Jn(e))return c0(e);if(Bi(e))return l0(e);if(Hi(e))return sg(e);if(Gi(e))return u0(e);if(qi(e))return d0(e)}throw $i(e)}function a0(e){return new W(n=>{let t=e[Qn]();if(k(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function c0(e){return new W(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function l0(e){return new W(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,Ai)})}function u0(e){return new W(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function sg(e){return new W(n=>{f0(e,n).catch(t=>n.error(t))})}function d0(e){return sg(Wi(e))}function f0(e,n){var t,r,o,i;return rg(this,void 0,void 0,function*(){try{for(t=ig(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function we(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Zi(e,n=0){return B((t,r)=>{t.subscribe(j(r,o=>we(r,e,()=>r.next(o),n),()=>we(r,e,()=>r.complete(),n),o=>we(r,e,()=>r.error(o),n)))})}function Yi(e,n=0){return B((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function ag(e,n){return te(e).pipe(Yi(n),Zi(n))}function cg(e,n){return te(e).pipe(Yi(n),Zi(n))}function lg(e,n){return new W(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function ug(e,n){return new W(t=>{let r;return we(t,n,()=>{r=e[zi](),we(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>k(r?.return)&&r.return()})}function Qi(e,n){if(!e)throw new Error("Iterable cannot be null");return new W(t=>{we(t,n,()=>{let r=e[Symbol.asyncIterator]();we(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function dg(e,n){return Qi(Wi(e),n)}function fg(e,n){if(e!=null){if(Ui(e))return ag(e,n);if(Jn(e))return lg(e,n);if(Bi(e))return cg(e,n);if(Hi(e))return Qi(e,n);if(Gi(e))return ug(e,n);if(qi(e))return dg(e,n)}throw $i(e)}function ne(e,n){return n?fg(e,n):te(e)}function O(...e){let n=qt(e);return ne(e,n)}function er(e,n){let t=k(e)?e:()=>e,r=o=>o.error(t());return new W(n?o=>n.schedule(r,0,o):r)}function rl(e){return!!e&&(e instanceof W||k(e.lift)&&k(e.subscribe))}var h0=Gt(e=>function(t=null){e(this),this.message="Timeout has occurred",this.name="TimeoutError",this.info=t});function p0(e,n){let{first:t,each:r,with:o=g0,scheduler:i=n??to,meta:s=null}=Pi(e)?{first:e}:typeof e=="number"?{each:e}:e;if(t==null&&r==null)throw new TypeError("No timeout provided.");return B((a,c)=>{let l,d,h=null,p=0,f=y=>{d=we(c,i,()=>{try{l.unsubscribe(),te(o({meta:s,lastValue:h,seen:p})).subscribe(c)}catch(S){c.error(S)}},y)};l=a.subscribe(j(c,y=>{d?.unsubscribe(),p++,c.next(h=y),r>0&&f(r)},void 0,void 0,()=>{d?.closed||d?.unsubscribe(),h=null})),!p&&f(t!=null?typeof t=="number"?t:+t-i.now():r)})}function g0(e){throw new h0(e)}function U(e,n){return B((t,r)=>{let o=0;t.subscribe(j(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:m0}=Array;function v0(e,n){return m0(n)?e(...n):e(n)}function tr(e){return U(n=>v0(e,n))}var{isArray:y0}=Array,{getPrototypeOf:D0,prototype:I0,keys:C0}=Object;function Ki(e){if(e.length===1){let n=e[0];if(y0(n))return{args:n,keys:null};if(b0(n)){let t=C0(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function b0(e){return e&&typeof e=="object"&&D0(e)===I0}function Xi(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function yn(...e){let n=qt(e),t=Vi(e),{args:r,keys:o}=Ki(e);if(r.length===0)return ne([],n);let i=new W(w0(r,n,o?s=>Xi(o,s):Ie));return t?i.pipe(tr(t)):i}function w0(e,n,t=Ie){return r=>{hg(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)hg(n,()=>{let l=ne(e[c],n),d=!1;l.subscribe(j(r,h=>{i[c]=h,d||(d=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function hg(e,n,t){e?we(t,e,n):n()}function pg(e,n,t,r,o,i,s,a){let c=[],l=0,d=0,h=!1,p=()=>{h&&!c.length&&!l&&n.complete()},f=S=>l<r?y(S):c.push(S),y=S=>{i&&n.next(S),l++;let N=!1;te(t(S,d++)).subscribe(j(n,z=>{o?.(z),i?f(z):n.next(z)},()=>{N=!0},void 0,()=>{if(N)try{for(l--;c.length&&l<r;){let z=c.shift();s?we(n,s,()=>y(z)):y(z)}p()}catch(z){n.error(z)}}))};return e.subscribe(j(n,f,()=>{h=!0,p()})),()=>{a?.()}}function ae(e,n,t=1/0){return k(n)?ae((r,o)=>U((i,s)=>n(r,i,o,s))(te(e(r,o))),t):(typeof n=="number"&&(t=n),B((r,o)=>pg(r,o,e,t)))}function nr(e=1/0){return ae(Ie,e)}function gg(){return nr(1)}function rr(...e){return gg()(ne(e,qt(e)))}function Ji(e){return new W(n=>{te(e()).subscribe(n)})}function ol(...e){let n=Vi(e),{args:t,keys:r}=Ki(e),o=new W(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let d=0;d<s;d++){let h=!1;te(t[d]).subscribe(j(i,p=>{h||(h=!0,l--),a[d]=p},()=>c--,void 0,()=>{(!c||!h)&&(l||i.next(r?Xi(r,a):a),i.complete())}))}});return n?o.pipe(tr(n)):o}var E0=["addListener","removeListener"],_0=["addEventListener","removeEventListener"],M0=["on","off"];function Dn(e,n,t,r){if(k(t)&&(r=t,t=void 0),r)return Dn(e,n,t).pipe(tr(r));let[o,i]=x0(e)?_0.map(s=>a=>e[s](n,a,t)):S0(e)?E0.map(mg(e,n)):T0(e)?M0.map(mg(e,n)):[];if(!o&&Jn(e))return ae(s=>Dn(s,n,t))(te(e));if(!o)throw new TypeError("Invalid event target");return new W(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function mg(e,n){return t=>r=>e[t](n,r)}function S0(e){return k(e.addListener)&&k(e.removeListener)}function T0(e){return k(e.on)&&k(e.off)}function x0(e){return k(e.addEventListener)&&k(e.removeEventListener)}function me(e,n){return B((t,r)=>{let o=0;t.subscribe(j(r,i=>e.call(n,i,o++)&&r.next(i)))})}function ut(e){return B((n,t)=>{let r=null,o=!1,i;r=n.subscribe(j(t,void 0,void 0,s=>{i=te(e(s,ut(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function vg(e,n,t,r,o){return(i,s)=>{let a=t,c=n,l=0;i.subscribe(j(s,d=>{let h=l++;c=a?e(c,d,h):(a=!0,d),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function dt(e,n){return k(n)?ae(e,n,1):ae(e,1)}function Zt(e){return B((n,t)=>{let r=!1;n.subscribe(j(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function At(e){return e<=0?()=>Re:B((n,t)=>{let r=0;n.subscribe(j(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function il(e,n=Ie){return e=e??A0,B((t,r)=>{let o,i=!0;t.subscribe(j(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function A0(e,n){return e===n}function es(e=R0){return B((n,t)=>{let r=!1;n.subscribe(j(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function R0(){return new et}function Yt(e){return B((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function Rt(e,n){let t=arguments.length>=2;return r=>r.pipe(e?me((o,i)=>e(o,i,r)):Ie,At(1),t?Zt(n):es(()=>new et))}function or(e){return e<=0?()=>Re:B((n,t)=>{let r=[];n.subscribe(j(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function sl(e,n){let t=arguments.length>=2;return r=>r.pipe(e?me((o,i)=>e(o,i,r)):Ie,or(1),t?Zt(n):es(()=>new et))}function N0(e=1/0){let n;e&&typeof e=="object"?n=e:n={count:e};let{count:t=1/0,delay:r,resetOnSuccess:o=!1}=n;return t<=0?Ie:B((i,s)=>{let a=0,c,l=()=>{let d=!1;c=i.subscribe(j(s,h=>{o&&(a=0),s.next(h)},void 0,h=>{if(a++<t){let p=()=>{c?(c.unsubscribe(),c=null,l()):d=!0};if(r!=null){let f=typeof r=="number"?Li(r):te(r(h,a)),y=j(s,()=>{y.unsubscribe(),p()},()=>{s.complete()});f.subscribe(y)}else p()}else s.error(h)})),d&&(c.unsubscribe(),c=null,l())};l()})}function al(e,n){return B(vg(e,n,arguments.length>=2,!0))}function cl(...e){let n=qt(e);return B((t,r)=>{(n?rr(e,t,n):rr(e,t)).subscribe(r)})}function ve(e,n){return B((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(j(r,c=>{o?.unsubscribe();let l=0,d=i++;te(e(c,d)).subscribe(o=j(r,h=>r.next(n?n(c,h,d,l++):h),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function ll(e){return B((n,t)=>{te(e).subscribe(j(t,()=>t.complete(),Jr)),!t.closed&&n.subscribe(t)})}function Ce(e,n,t){let r=k(e)||n||t?{next:e,error:n,complete:t}:e;return r?B((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(j(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Ie}function hl(e,n){return Object.is(e,n)}var he=null,ts=!1,pl=1,We=Symbol("SIGNAL");function q(e){let n=he;return he=e,n}function gl(){return he}var ro={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function oo(e){if(ts)throw new Error("");if(he===null)return;he.consumerOnSignalRead(e);let n=he.nextProducerIndex++;if(is(he),n<he.producerNode.length&&he.producerNode[n]!==e&&no(he)){let t=he.producerNode[n];os(t,he.producerIndexOfThis[n])}he.producerNode[n]!==e&&(he.producerNode[n]=e,he.producerIndexOfThis[n]=no(he)?Dg(e,he,n):0),he.producerLastReadVersion[n]=e.version}function yg(){pl++}function ml(e){if(!(no(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===pl)){if(!e.producerMustRecompute(e)&&!Il(e)){fl(e);return}e.producerRecomputeValue(e),fl(e)}}function vl(e){if(e.liveConsumerNode===void 0)return;let n=ts;ts=!0;try{for(let t of e.liveConsumerNode)t.dirty||O0(t)}finally{ts=n}}function yl(){return he?.consumerAllowSignalWrites!==!1}function O0(e){e.dirty=!0,vl(e),e.consumerMarkedDirty?.(e)}function fl(e){e.dirty=!1,e.lastCleanEpoch=pl}function rs(e){return e&&(e.nextProducerIndex=0),q(e)}function Dl(e,n){if(q(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(no(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)os(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Il(e){is(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(ml(t),r!==t.version))return!0}return!1}function Cl(e){if(is(e),no(e))for(let n=0;n<e.producerNode.length;n++)os(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Dg(e,n,t){if(Ig(e),e.liveConsumerNode.length===0&&Cg(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Dg(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function os(e,n){if(Ig(e),e.liveConsumerNode.length===1&&Cg(e))for(let r=0;r<e.producerNode.length;r++)os(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];is(o),o.producerIndexOfThis[r]=n}}function no(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function is(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Ig(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Cg(e){return e.producerNode!==void 0}function bl(e,n){let t=Object.create(k0);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(ml(t),oo(t),t.value===ns)throw t.error;return t.value};return r[We]=t,r}var ul=Symbol("UNSET"),dl=Symbol("COMPUTING"),ns=Symbol("ERRORED"),k0=P(I({},ro),{value:ul,dirty:!0,error:null,equal:hl,kind:"computed",producerMustRecompute(e){return e.value===ul||e.value===dl},producerRecomputeValue(e){if(e.value===dl)throw new Error("Detected cycle in computations.");let n=e.value;e.value=dl;let t=rs(e),r,o=!1;try{r=e.computation(),q(null),o=n!==ul&&n!==ns&&r!==ns&&e.equal(n,r)}catch(i){r=ns,e.error=i}finally{Dl(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function F0(){throw new Error}var bg=F0;function wg(e){bg(e)}function wl(e){bg=e}var P0=null;function El(e,n){let t=Object.create(ss);t.value=e,n!==void 0&&(t.equal=n);let r=()=>(oo(t),t.value);return r[We]=t,r}function io(e,n){yl()||wg(e),e.equal(e.value,n)||(e.value=n,L0(e))}function _l(e,n){yl()||wg(e),io(e,n(e.value))}var ss=P(I({},ro),{equal:hl,value:void 0,kind:"signal"});function L0(e){e.version++,yg(),vl(e),P0?.()}function Ml(e){let n=q(null);try{return e()}finally{q(n)}}var Sl;function so(){return Sl}function Nt(e){let n=Sl;return Sl=e,n}var as=Symbol("NotFound");var Cm="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",x=class extends Error{code;constructor(n,t){super(bm(n,t)),this.code=n}};function U0(e){return`NG0${Math.abs(e)}`}function bm(e,n){return`${U0(e)}${n?": "+n:""}`}var wm=Symbol("InputSignalNode#UNSET"),H0=P(I({},ss),{transformFn:void 0,applyValueToInputSignal(e,n){io(e,n)}});function Em(e,n){let t=Object.create(H0);t.value=e,t.transformFn=n?.transform;function r(){if(oo(t),t.value===wm){let o=null;throw new x(-950,o)}return t.value}return r[We]=t,r}function Er(e){return{toString:e}.toString()}var ir="__annotations__",sr="__parameters__",Eg="__prop__metadata__";function $0(e,n,t,r,o){return Er(()=>{let i=_m(n);function s(...a){if(this instanceof s)return i.call(this,...a),this;let c=new s(...a);return function(d){return o&&o(d,...a),(d.hasOwnProperty(ir)?d[ir]:Object.defineProperty(d,ir,{value:[]})[ir]).push(c),d}}return t&&(s.prototype=Object.create(t.prototype)),s.prototype.ngMetadataName=e,s.annotationCls=s,s})}function _m(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function _r(e,n,t){return Er(()=>{let r=_m(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,d){let h=c.hasOwnProperty(sr)?c[sr]:Object.defineProperty(c,sr,{value:[]})[sr];for(;h.length<=d;)h.push(null);return(h[d]=h[d]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Oe=globalThis;function K(e){for(let n in e)if(e[n]===K)return n;throw Error("Could not find renamed property on target object.")}function z0(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function Ne(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(Ne).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function _g(e,n){return e?n?`${e} ${n}`:e:n||""}var G0=K({__forward_ref__:K});function Be(e){return e.__forward_ref__=Be,e.toString=function(){return Ne(this())},e}function Ee(e){return Mm(e)?e():e}function Mm(e){return typeof e=="function"&&e.hasOwnProperty(G0)&&e.__forward_ref__===Be}function T(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Me(e){return{providers:e.providers||[],imports:e.imports||[]}}function qs(e){return Mg(e,Is)||Mg(e,Tm)}function Sm(e){return qs(e)!==null}function Mg(e,n){return e.hasOwnProperty(n)?e[n]:null}function W0(e){let n=e&&(e[Is]||e[Tm]);return n||null}function Sg(e){return e&&(e.hasOwnProperty(Tg)||e.hasOwnProperty(q0))?e[Tg]:null}var Is=K({\u0275prov:K}),Tg=K({\u0275inj:K}),Tm=K({ngInjectableDef:K}),q0=K({ngInjectorDef:K}),A=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=T({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function xm(e){return e&&!!e.\u0275providers}var Z0=K({\u0275cmp:K}),Y0=K({\u0275dir:K}),Q0=K({\u0275pipe:K}),K0=K({\u0275mod:K}),hr=K({\u0275fac:K}),fo=K({__NG_ELEMENT_ID__:K}),xg=K({__NG_ENV_ID__:K});function bn(e){return typeof e=="string"?e:e==null?"":String(e)}function X0(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():bn(e)}function Am(e,n){throw new x(-200,e)}function Bu(e,n){throw new x(-201,!1)}var H=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(H||{}),Hl;function Rm(){return Hl}function qe(e){let n=Hl;return Hl=e,n}function Nm(e,n,t){let r=qs(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&H.Optional)return null;if(n!==void 0)return n;Bu(e,"Injector")}var J0={},In=J0,$l="__NG_DI_FLAG__",Cs=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=t;return this.injector.get(n,r.optional?as:In,r)}},bs="ngTempTokenPath",eb="ngTokenPath",tb=/\n/gm,nb="\u0275",Ag="__source";function rb(e,n=H.Default){if(so()===void 0)throw new x(-203,!1);if(so()===null)return Nm(e,void 0,n);{let t=so(),r;return t instanceof Cs?r=t.injector:r=t,r.get(e,n&H.Optional?null:void 0,n)}}function R(e,n=H.Default){return(Rm()||rb)(Ee(e),n)}function ob(e){throw new x(202,!1)}function v(e,n=H.Default){return R(e,Zs(n))}function Zs(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function zl(e){let n=[];for(let t=0;t<e.length;t++){let r=Ee(e[t]);if(Array.isArray(r)){if(r.length===0)throw new x(900,!1);let o,i=H.Default;for(let s=0;s<r.length;s++){let a=r[s],c=ib(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(R(o,i))}else n.push(R(r))}return n}function Co(e,n){return e[$l]=n,e.prototype[$l]=n,e}function ib(e){return e[$l]}function sb(e,n,t,r){let o=e[bs];throw n[Ag]&&o.unshift(n[Ag]),e.message=ab(`
`+e.message,o,t,r),e[eb]=o,e[bs]=null,e}function ab(e,n,t,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==nb?e.slice(2):e;let o=Ne(n);if(Array.isArray(n))o=n.map(Ne).join(" -> ");else if(typeof n=="object"){let i=[];for(let s in n)if(n.hasOwnProperty(s)){let a=n[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ne(a)))}o=`{${i.join(", ")}}`}return`${t}${r?"("+r+")":""}[${o}]: ${e.replace(tb,`
  `)}`}var cb=Co(_r("Inject",e=>({token:e})),-1),Om=Co(_r("Optional"),8),lb=Co(_r("Self"),2),km=Co(_r("SkipSelf"),4),ub=Co(_r("Host"),1);function pr(e,n){let t=e.hasOwnProperty(hr);return t?e[hr]:null}function db(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function fb(e){return e.flat(Number.POSITIVE_INFINITY)}function Uu(e,n){e.forEach(t=>Array.isArray(t)?Uu(t,n):n(t))}function Fm(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function ws(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function ps(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function hb(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function pb(e,n,t){let r=bo(e,n);return r>=0?e[r|1]=t:(r=~r,hb(e,r,n,t)),r}function Tl(e,n){let t=bo(e,n);if(t>=0)return e[t|1]}function bo(e,n){return gb(e,n,1)}function gb(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var wn={},Ze=[],po=new A(""),Pm=new A("",-1),Lm=new A(""),Es=class{get(n,t=In){if(t===In){let r=new Error(`NullInjectorError: No provider for ${Ne(n)}!`);throw r.name="NullInjectorError",r}return t}};function jm(e,n){let t=e[K0]||null;if(!t&&n===!0)throw new Error(`Type ${Ne(e)} does not have '\u0275mod' property.`);return t}function Xt(e){return e[Z0]||null}function mb(e){return e[Y0]||null}function vb(e){return e[Q0]||null}function Ys(e){return{\u0275providers:e}}function yb(...e){return{\u0275providers:Vm(!0,e),\u0275fromNgModule:!0}}function Vm(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return Uu(n,s=>{let a=s;Gl(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Bm(o,i),t}function Bm(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Hu(o,i=>{n(i,r)})}}function Gl(e,n,t,r){if(e=Ee(e),!e)return!1;let o=null,i=Sg(e),s=!i&&Xt(e);if(!i&&!s){let c=e.ngModule;if(i=Sg(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Gl(l,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Uu(i.imports,d=>{Gl(d,n,t,r)&&(l||=[],l.push(d))})}finally{}l!==void 0&&Bm(l,n)}if(!a){let l=pr(o)||(()=>new o);n({provide:o,useFactory:l,deps:Ze},o),n({provide:Lm,useValue:o,multi:!0},o),n({provide:po,useValue:()=>R(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Hu(c,d=>{n(d,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Hu(e,n){for(let t of e)xm(t)&&(t=t.\u0275providers),Array.isArray(t)?Hu(t,n):n(t)}var Db=K({provide:String,useValue:K});function Um(e){return e!==null&&typeof e=="object"&&Db in e}function Ib(e){return!!(e&&e.useExisting)}function Cb(e){return!!(e&&e.useFactory)}function gr(e){return typeof e=="function"}function bb(e){return!!e.useClass}var Qs=new A(""),gs={},Rg={},xl;function Ks(){return xl===void 0&&(xl=new Es),xl}var ie=class{},go=class extends ie{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,ql(n,s=>this.processProvider(s)),this.records.set(Pm,ar(void 0,this)),o.has("environment")&&this.records.set(ie,ar(void 0,this));let i=this.records.get(Qs);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Lm,Ze,H.Self))}retrieve(n,t){let r=t;return this.get(n,r.optional?as:In,r)}destroy(){co(this),this._destroyed=!0;let n=q(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),q(n)}}onDestroy(n){return co(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){co(this);let t=Nt(this),r=qe(void 0),o;try{return n()}finally{Nt(t),qe(r)}}get(n,t=In,r=H.Default){if(co(this),n.hasOwnProperty(xg))return n[xg](this);r=Zs(r);let o,i=Nt(this),s=qe(void 0);try{if(!(r&H.SkipSelf)){let c=this.records.get(n);if(c===void 0){let l=Sb(n)&&qs(n);l&&this.injectableDefInScope(l)?c=ar(Wl(n),gs):c=null,this.records.set(n,c)}if(c!=null)return this.hydrate(n,c)}let a=r&H.Self?Ks():this.parent;return t=r&H.Optional&&t===In?null:t,a.get(n,t)}catch(a){if(a.name==="NullInjectorError"){if((a[bs]=a[bs]||[]).unshift(Ne(n)),i)throw a;return sb(a,n,"R3InjectorError",this.source)}else throw a}finally{qe(s),Nt(i)}}resolveInjectorInitializers(){let n=q(null),t=Nt(this),r=qe(void 0),o;try{let i=this.get(po,Ze,H.Self);for(let s of i)s()}finally{Nt(t),qe(r),q(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(Ne(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=Ee(n);let t=gr(n)?n:Ee(n&&n.provide),r=Eb(n);if(!gr(n)&&n.multi===!0){let o=this.records.get(t);o||(o=ar(void 0,gs,!0),o.factory=()=>zl(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t){let r=q(null);try{return t.value===Rg?Am(Ne(n)):t.value===gs&&(t.value=Rg,t.value=t.factory()),typeof t.value=="object"&&t.value&&Mb(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{q(r)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=Ee(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Wl(e){let n=qs(e),t=n!==null?n.factory:pr(e);if(t!==null)return t;if(e instanceof A)throw new x(204,!1);if(e instanceof Function)return wb(e);throw new x(204,!1)}function wb(e){if(e.length>0)throw new x(204,!1);let t=W0(e);return t!==null?()=>t.factory(e):()=>new e}function Eb(e){if(Um(e))return ar(void 0,e.useValue);{let n=Hm(e);return ar(n,gs)}}function Hm(e,n,t){let r;if(gr(e)){let o=Ee(e);return pr(o)||Wl(o)}else if(Um(e))r=()=>Ee(e.useValue);else if(Cb(e))r=()=>e.useFactory(...zl(e.deps||[]));else if(Ib(e))r=()=>R(Ee(e.useExisting));else{let o=Ee(e&&(e.useClass||e.provide));if(_b(e))r=()=>new o(...zl(e.deps));else return pr(o)||Wl(o)}return r}function co(e){if(e.destroyed)throw new x(205,!1)}function ar(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function _b(e){return!!e.deps}function Mb(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function Sb(e){return typeof e=="function"||typeof e=="object"&&e instanceof A}function ql(e,n){for(let t of e)Array.isArray(t)?ql(t,n):t&&xm(t)?ql(t.\u0275providers,n):n(t)}function Fe(e,n){let t;e instanceof go?(co(e),t=e):t=new Cs(e);let r,o=Nt(t),i=qe(void 0);try{return n()}finally{Nt(o),qe(i)}}function $m(){return Rm()!==void 0||so()!=null}function Tb(e){if(!$m())throw new x(-203,!1)}function Zl(e){let n=Oe.ng;if(n&&n.\u0275compilerFacade)return n.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}var Ng={\u0275\u0275defineInjectable:T,\u0275\u0275defineInjector:Me,\u0275\u0275inject:R,\u0275\u0275invalidFactoryDep:ob,resolveForwardRef:Ee},xb=Function;function lo(e){return typeof e=="function"}var Ab=/^function\s+\S+\(\)\s*{[\s\S]+\.apply\(this,\s*(arguments|(?:[^()]+\(\[\],)?[^()]+\(arguments\).*)\)/,Rb=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{/,Nb=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(/,Ob=/^class\s+[A-Za-z\d$_]*\s*extends\s+[^{]+{[\s\S]*constructor\s*\(\)\s*{[^}]*super\(\.\.\.arguments\)/;function kb(e){return Ab.test(e)||Ob.test(e)||Rb.test(e)&&!Nb.test(e)}var Yl=class{_reflect;constructor(n){this._reflect=n||Oe.Reflect}factory(n){return(...t)=>new n(...t)}_zipTypesAndAnnotations(n,t){let r;typeof n>"u"?r=ps(t.length):r=ps(n.length);for(let o=0;o<r.length;o++)typeof n>"u"?r[o]=[]:n[o]&&n[o]!=Object?r[o]=[n[o]]:r[o]=[],t&&t[o]!=null&&(r[o]=r[o].concat(t[o]));return r}_ownParameters(n,t){let r=n.toString();if(kb(r))return null;if(n.parameters&&n.parameters!==t.parameters)return n.parameters;let o=n.ctorParameters;if(o&&o!==t.ctorParameters){let a=typeof o=="function"?o():o,c=a.map(d=>d&&d.type),l=a.map(d=>d&&Al(d.decorators));return this._zipTypesAndAnnotations(c,l)}let i=n.hasOwnProperty(sr)&&n[sr],s=this._reflect&&this._reflect.getOwnMetadata&&this._reflect.getOwnMetadata("design:paramtypes",n);return s||i?this._zipTypesAndAnnotations(s,i):ps(n.length)}parameters(n){if(!lo(n))return[];let t=cs(n),r=this._ownParameters(n,t);return!r&&t!==Object&&(r=this.parameters(t)),r||[]}_ownAnnotations(n,t){if(n.annotations&&n.annotations!==t.annotations){let r=n.annotations;return typeof r=="function"&&r.annotations&&(r=r.annotations),r}return n.decorators&&n.decorators!==t.decorators?Al(n.decorators):n.hasOwnProperty(ir)?n[ir]:null}annotations(n){if(!lo(n))return[];let t=cs(n),r=this._ownAnnotations(n,t)||[];return(t!==Object?this.annotations(t):[]).concat(r)}_ownPropMetadata(n,t){if(n.propMetadata&&n.propMetadata!==t.propMetadata){let r=n.propMetadata;return typeof r=="function"&&r.propMetadata&&(r=r.propMetadata),r}if(n.propDecorators&&n.propDecorators!==t.propDecorators){let r=n.propDecorators,o={};return Object.keys(r).forEach(i=>{o[i]=Al(r[i])}),o}return n.hasOwnProperty(Eg)?n[Eg]:null}propMetadata(n){if(!lo(n))return{};let t=cs(n),r={};if(t!==Object){let i=this.propMetadata(t);Object.keys(i).forEach(s=>{r[s]=i[s]})}let o=this._ownPropMetadata(n,t);return o&&Object.keys(o).forEach(i=>{let s=[];r.hasOwnProperty(i)&&s.push(...r[i]),s.push(...o[i]),r[i]=s}),r}ownPropMetadata(n){return lo(n)?this._ownPropMetadata(n,cs(n))||{}:{}}hasLifecycleHook(n,t){return n instanceof xb&&t in n.prototype}};function Al(e){return e?e.map(n=>{let r=n.type.annotationCls,o=n.args?n.args:[];return new r(...o)}):[]}function cs(e){let n=e.prototype?Object.getPrototypeOf(e.prototype):null;return(n?n.constructor:null)||Object}var Pt=0,G=1,L=2,_e=3,rt=4,Pe=5,mo=6,_s=7,ke=8,mr=9,Ot=10,ue=11,vo=12,Og=13,Mr=14,Qe=15,En=16,cr=17,kt=18,Xs=19,zm=20,Qt=21,Rl=22,Ms=23,Ye=24,dr=25,Ke=26,Gm=1;var _n=7,Ss=8,vr=9,Ve=10;function Kt(e){return Array.isArray(e)&&typeof e[Gm]=="object"}function Lt(e){return Array.isArray(e)&&e[Gm]===!0}function $u(e){return(e.flags&4)!==0}function Sr(e){return e.componentOffset>-1}function Js(e){return(e.flags&1)===1}function ht(e){return!!e.template}function Ts(e){return(e[L]&512)!==0}function wo(e){return(e[L]&256)===256}var Ql=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Wm(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Xe=(()=>{let e=()=>qm;return e.ngInherit=!0,e})();function qm(e){return e.type.prototype.ngOnChanges&&(e.setInput=Pb),Fb}function Fb(){let e=Ym(this),n=e?.current;if(n){let t=e.previous;if(t===wn)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function Pb(e,n,t,r,o){let i=this.declaredInputs[r],s=Ym(e)||Lb(e,{previous:wn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Ql(l&&l.currentValue,t,c===wn),Wm(e,n,o,t)}var Zm="__ngSimpleChanges__";function Ym(e){return e[Zm]||null}function Lb(e,n){return e[Zm]=n}var kg=null;var re=function(e,n=null,t){kg?.(e,n,t)},jb="svg",Vb="math";function pt(e){for(;Array.isArray(e);)e=e[Pt];return e}function Qm(e,n){return pt(n[e])}function It(e,n){return pt(n[e.index])}function Km(e,n){return e.data[n]}function gt(e,n){let t=n[e];return Kt(t)?t:t[Pt]}function Bb(e){return(e[L]&4)===4}function zu(e){return(e[L]&128)===128}function Ub(e){return Lt(e[_e])}function yr(e,n){return n==null?null:e[n]}function Xm(e){e[cr]=0}function Jm(e){e[L]&1024||(e[L]|=1024,zu(e)&&Eo(e))}function Hb(e,n){for(;e>0;)n=n[Mr],e--;return n}function ea(e){return!!(e[L]&9216||e[Ye]?.dirty)}function Kl(e){e[Ot].changeDetectionScheduler?.notify(8),e[L]&64&&(e[L]|=1024),ea(e)&&Eo(e)}function Eo(e){e[Ot].changeDetectionScheduler?.notify(0);let n=Mn(e);for(;n!==null&&!(n[L]&8192||(n[L]|=8192,!zu(n)));)n=Mn(n)}function ev(e,n){if(wo(e))throw new x(911,!1);e[Qt]===null&&(e[Qt]=[]),e[Qt].push(n)}function $b(e,n){if(e[Qt]===null)return;let t=e[Qt].indexOf(n);t!==-1&&e[Qt].splice(t,1)}function Mn(e){let n=e[_e];return Lt(n)?n[_e]:n}function Gu(e){return e[_s]??=[]}function Wu(e){return e.cleanup??=[]}function zb(e,n,t,r){let o=Gu(n);o.push(t),e.firstCreatePass&&Wu(e).push(r,o.length-1)}var $={lFrame:cv(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var Xl=!1;function Gb(){return $.lFrame.elementDepthCount}function Wb(){$.lFrame.elementDepthCount++}function qb(){$.lFrame.elementDepthCount--}function qu(){return $.bindingsEnabled}function tv(){return $.skipHydrationRootTNode!==null}function Zb(e){return $.skipHydrationRootTNode===e}function Yb(){$.skipHydrationRootTNode=null}function Z(){return $.lFrame.lView}function fe(){return $.lFrame.tView}function Zu(e){return $.lFrame.contextLView=e,e[ke]}function Yu(e){return $.lFrame.contextLView=null,e}function Se(){let e=nv();for(;e!==null&&e.type===64;)e=e.parent;return e}function nv(){return $.lFrame.currentTNode}function Qb(){let e=$.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function Nn(e,n){let t=$.lFrame;t.currentTNode=e,t.isParent=n}function Qu(){return $.lFrame.isParent}function Ku(){$.lFrame.isParent=!1}function rv(){return Xl}function Fg(e){let n=Xl;return Xl=e,n}function Kb(){let e=$.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function Xb(){return $.lFrame.bindingIndex}function Jb(e){return $.lFrame.bindingIndex=e}function ta(){return $.lFrame.bindingIndex++}function ov(e){let n=$.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function ew(){return $.lFrame.inI18n}function tw(e,n){let t=$.lFrame;t.bindingIndex=t.bindingRootIndex=e,Jl(n)}function nw(){return $.lFrame.currentDirectiveIndex}function Jl(e){$.lFrame.currentDirectiveIndex=e}function rw(e){let n=$.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function iv(){return $.lFrame.currentQueryIndex}function Xu(e){$.lFrame.currentQueryIndex=e}function ow(e){let n=e[G];return n.type===2?n.declTNode:n.type===1?e[Pe]:null}function sv(e,n,t){if(t&H.SkipSelf){let o=n,i=e;for(;o=o.parent,o===null&&!(t&H.Host);)if(o=ow(i),o===null||(i=i[Mr],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=$.lFrame=av();return r.currentTNode=n,r.lView=e,!0}function Ju(e){let n=av(),t=e[G];$.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function av(){let e=$.lFrame,n=e===null?null:e.child;return n===null?cv(e):n}function cv(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function lv(){let e=$.lFrame;return $.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var uv=lv;function ed(){let e=lv();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function iw(e){return($.lFrame.contextLView=Hb(e,$.lFrame.contextLView))[ke]}function On(){return $.lFrame.selectedIndex}function Sn(e){$.lFrame.selectedIndex=e}function na(){let e=$.lFrame;return Km(e.tView,e.selectedIndex)}function sw(){return $.lFrame.currentNamespace}var dv=!0;function ra(){return dv}function oa(e){dv=e}function aw(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=qm(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function td(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:d}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),l&&((e.viewHooks??=[]).push(t,l),(e.viewCheckHooks??=[]).push(t,l)),d!=null&&(e.destroyHooks??=[]).push(t,d)}}function ms(e,n,t){fv(e,n,3,t)}function vs(e,n,t,r){(e[L]&3)===t&&fv(e,n,t,r)}function Nl(e,n){let t=e[L];(t&3)===n&&(t&=16383,t+=1,e[L]=t)}function fv(e,n,t,r){let o=r!==void 0?e[cr]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[cr]+=65536),(a<i||i==-1)&&(cw(e,t,n,c),e[cr]=(e[cr]&**********)+c+2),c++}function Pg(e,n){re(4,e,n);let t=q(null);try{n.call(e)}finally{q(t),re(5,e,n)}}function cw(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[L]>>14<e[cr]>>16&&(e[L]&3)===n&&(e[L]+=16384,Pg(a,i)):Pg(a,i)}var fr=-1,Tn=class{factory;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r){this.factory=n,this.canSeeViewProviders=t,this.injectImpl=r}};function lw(e){return(e.flags&8)!==0}function uw(e){return(e.flags&16)!==0}function dw(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];fw(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function hv(e){return e===3||e===4||e===6}function fw(e){return e.charCodeAt(0)===64}function Dr(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?Lg(e,t,o,null,n[++r]):Lg(e,t,o,null,null))}}return e}function Lg(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function pv(e){return e!==fr}function xs(e){return e&32767}function hw(e){return e>>16}function As(e,n){let t=hw(e),r=n;for(;t>0;)r=r[Mr],t--;return r}var eu=!0;function jg(e){let n=eu;return eu=e,n}var pw=256,gv=pw-1,mv=5,gw=0,ft={};function mw(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(fo)&&(r=t[fo]),r==null&&(r=t[fo]=gw++);let o=r&gv,i=1<<o;n.data[e+(o>>mv)]|=i}function Rs(e,n){let t=vv(e,n);if(t!==-1)return t;let r=n[G];r.firstCreatePass&&(e.injectorIndex=n.length,Ol(r.data,e),Ol(n,null),Ol(r.blueprint,null));let o=nd(e,n),i=e.injectorIndex;if(pv(o)){let s=xs(o),a=As(o,n),c=a[G].data;for(let l=0;l<8;l++)n[i+l]=a[s+l]|c[s+l]}return n[i+8]=o,i}function Ol(e,n){e.push(0,0,0,0,0,0,0,0,n)}function vv(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function nd(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=bv(o),r===null)return fr;if(t++,o=o[Mr],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return fr}function tu(e,n,t){mw(e,n,t)}function vw(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(hv(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function yv(e,n,t){if(t&H.Optional||e!==void 0)return e;Bu(n,"NodeInjector")}function Dv(e,n,t,r){if(t&H.Optional&&r===void 0&&(r=null),(t&(H.Self|H.Host))===0){let o=e[mr],i=qe(void 0);try{return o?o.get(n,r,t&H.Optional):Nm(n,r,t&H.Optional)}finally{qe(i)}}return yv(r,n,t)}function Iv(e,n,t,r=H.Default,o){if(e!==null){if(n[L]&2048&&!(r&H.Self)){let s=Cw(e,n,t,r,ft);if(s!==ft)return s}let i=Cv(e,n,t,r,ft);if(i!==ft)return i}return Dv(n,t,r,o)}function Cv(e,n,t,r,o){let i=Dw(t);if(typeof i=="function"){if(!sv(n,e,r))return r&H.Host?yv(o,t,r):Dv(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&H.Optional))Bu(t);else return s}finally{uv()}}else if(typeof i=="number"){let s=null,a=vv(e,n),c=fr,l=r&H.Host?n[Qe][Pe]:null;for((a===-1||r&H.SkipSelf)&&(c=a===-1?nd(e,n):n[a+8],c===fr||!Bg(r,!1)?a=-1:(s=n[G],a=xs(c),n=As(c,n)));a!==-1;){let d=n[G];if(Vg(i,a,d.data)){let h=yw(a,n,t,s,r,l);if(h!==ft)return h}c=n[a+8],c!==fr&&Bg(r,n[G].data[a+8]===l)&&Vg(i,a,n)?(s=d,a=xs(c),n=As(c,n)):a=-1}}return o}function yw(e,n,t,r,o,i){let s=n[G],a=s.data[e+8],c=r==null?Sr(a)&&eu:r!=s&&(a.type&3)!==0,l=o&H.Host&&i===a,d=ys(a,s,t,c,l);return d!==null?yo(n,s,d,a):ft}function ys(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,d=i>>20,h=r?a:a+d,p=o?a+d:l;for(let f=h;f<p;f++){let y=s[f];if(f<c&&t===y||f>=c&&y.type===t)return f}if(o){let f=s[c];if(f&&ht(f)&&f.type===t)return c}return null}function yo(e,n,t,r){let o=e[t],i=n.data;if(o instanceof Tn){let s=o;s.resolving&&Am(X0(i[t]));let a=jg(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?qe(s.injectImpl):null,d=sv(e,r,H.Default);try{o=e[t]=s.factory(void 0,i,e,r),n.firstCreatePass&&t>=r.directiveStart&&aw(t,i[t],n)}finally{l!==null&&qe(l),jg(a),s.resolving=!1,uv()}}return o}function Dw(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(fo)?e[fo]:void 0;return typeof n=="number"?n>=0?n&gv:Iw:n}function Vg(e,n,t){let r=1<<e;return!!(t[n+(e>>mv)]&r)}function Bg(e,n){return!(e&H.Self)&&!(e&H.Host&&n)}var Cn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Iv(this._tNode,this._lView,n,Zs(r),t)}};function Iw(){return new Cn(Se(),Z())}function be(e){return Er(()=>{let n=e.prototype.constructor,t=n[hr]||nu(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[hr]||nu(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function nu(e){return Mm(e)?()=>{let n=nu(Ee(e));return n&&n()}:pr(e)}function Cw(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[L]&2048&&!Ts(s);){let a=Cv(i,s,t,r|H.Self,ft);if(a!==ft)return a;let c=i.parent;if(!c){let l=s[zm];if(l){let d=l.get(t,ft,r);if(d!==ft)return d}c=bv(s),s=s[Mr]}i=c}return o}function bv(e){let n=e[G],t=n.type;return t===2?n.declTNode:t===1?e[Pe]:null}function Ct(e){return vw(Se(),e)}var bw=_r("Attribute",e=>({attributeName:e,__NG_ELEMENT_ID__:()=>Ct(e)})),Ug=null;function ww(){return Ug=Ug||new Yl}function Ew(e){return wv(ww().parameters(e))}function wv(e){return e.map(n=>_w(n))}function _w(e){let n={token:null,attribute:null,host:!1,optional:!1,self:!1,skipSelf:!1};if(Array.isArray(e)&&e.length>0)for(let t=0;t<e.length;t++){let r=e[t];if(r===void 0)continue;let o=Object.getPrototypeOf(r);if(r instanceof Om||o.ngMetadataName==="Optional")n.optional=!0;else if(r instanceof km||o.ngMetadataName==="SkipSelf")n.skipSelf=!0;else if(r instanceof lb||o.ngMetadataName==="Self")n.self=!0;else if(r instanceof ub||o.ngMetadataName==="Host")n.host=!0;else if(r instanceof cb)n.token=r.token;else if(r instanceof bw){if(r.attributeName===void 0)throw new x(204,!1);n.attribute=r.attributeName}else n.token=r}else e===void 0||Array.isArray(e)&&e.length===0?n.token=null:n.token=e;return n}function Mw(e,n){let t=null,r=null;e.hasOwnProperty(Is)||Object.defineProperty(e,Is,{get:()=>(t===null&&(t=Zl({usage:0,kind:"injectable",type:e}).compileInjectable(Ng,`ng:///${e.name}/\u0275prov.js`,Aw(e,n))),t)}),e.hasOwnProperty(hr)||Object.defineProperty(e,hr,{get:()=>{if(r===null){let o=Zl({usage:0,kind:"injectable",type:e});r=o.compileFactory(Ng,`ng:///${e.name}/\u0275fac.js`,{name:e.name,type:e,typeArgumentCount:0,deps:Ew(e),target:o.FactoryTarget.Injectable})}return r},configurable:!0})}var Sw=K({provide:String,useValue:K});function Hg(e){return e.useClass!==void 0}function Tw(e){return Sw in e}function $g(e){return e.useFactory!==void 0}function xw(e){return e.useExisting!==void 0}function Aw(e,n){let t=n||{providedIn:null},r={name:e.name,type:e,typeArgumentCount:0,providedIn:t.providedIn};return(Hg(t)||$g(t))&&t.deps!==void 0&&(r.deps=wv(t.deps)),Hg(t)?r.useClass=t.useClass:Tw(t)?r.useValue=t.useValue:$g(t)?r.useFactory=t.useFactory:xw(t)&&(r.useExisting=t.useExisting),r}var YB=$0("Injectable",void 0,void 0,void 0,(e,n)=>Mw(e,n));function zg(e,n=null,t=null,r){let o=Ev(e,n,t,r);return o.resolveInjectorInitializers(),o}function Ev(e,n=null,t=null,r,o=new Set){let i=[t||Ze,yb(e)];return r=r||(typeof e=="object"?void 0:Ne(e)),new go(i,n||Ks(),r||null,o)}var J=class e{static THROW_IF_NOT_FOUND=In;static NULL=new Es;static create(n,t){if(Array.isArray(n))return zg({name:""},t,n,"");{let r=n.name??"";return zg({name:r},n.parent,n.providers,r)}}static \u0275prov=T({token:e,providedIn:"any",factory:()=>R(Pm)});static __NG_ELEMENT_ID__=-1};var Rw=new A("");Rw.__NG_ELEMENT_ID__=e=>{let n=Se();if(n===null)throw new x(204,!1);if(n.type&2)return n.value;if(e&H.Optional)return null;throw new x(204,!1)};var _v=!1,_o=(()=>{class e{static __NG_ELEMENT_ID__=Nw;static __NG_ENV_ID__=t=>t}return e})(),ru=class extends _o{_lView;constructor(n){super(),this._lView=n}onDestroy(n){return ev(this._lView,n),()=>$b(this._lView,n)}};function Nw(){return new ru(Z())}var Ir=class{},Mv=new A("",{providedIn:"root",factory:()=>!1});var Sv=new A(""),Tv=new A(""),jt=(()=>{class e{taskId=0;pendingTasks=new Set;get _hasPendingTasks(){return this.hasPendingTasks.value}hasPendingTasks=new de(!1);add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})();var ou=class extends X{__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,$m()&&(this.destroyRef=v(_o,{optional:!0})??void 0,this.pendingTasks=v(jt,{optional:!0})??void 0)}emit(n){let t=q(null);try{super.next(n)}finally{q(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof le&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{n(t),r!==void 0&&this.pendingTasks?.remove(r)})}}},oe=ou;function Ns(...e){}function xv(e){let n,t;function r(){e=Ns;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Gg(e){return queueMicrotask(()=>e()),()=>{e=Ns}}var rd="isAngularZone",Os=rd+"_ID",Ow=0,g=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new oe(!1);onMicrotaskEmpty=new oe(!1);onStable=new oe(!1);onError=new oe(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=_v}=n;if(typeof Zone>"u")throw new x(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Pw(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(rd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new x(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new x(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,kw,Ns,Ns);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},kw={};function od(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Fw(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){xv(()=>{e.callbackScheduled=!1,iu(e),e.isCheckStableRunning=!0,od(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),iu(e)}function Pw(e){let n=()=>{Fw(e)},t=Ow++;e._inner=e._inner.fork({name:"angular",properties:{[rd]:!0,[Os]:t,[Os+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(Lw(c))return r.invokeTask(i,s,a,c);try{return Wg(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),qg(e)}},onInvoke:(r,o,i,s,a,c,l)=>{try{return Wg(e),r.invoke(i,s,a,c,l)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!jw(c)&&n(),qg(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,iu(e),od(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function iu(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Wg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function qg(e){e._nesting--,od(e)}var ks=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new oe;onMicrotaskEmpty=new oe;onStable=new oe;onError=new oe;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function Lw(e){return Av(e,"__ignore_ng_zone__")}function jw(e){return Av(e,"__scheduler_tick__")}function Av(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}function Vw(e="zone.js",n){return e==="noop"?new ks:e==="zone.js"?new g(n):e}var mt=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Bw=new A("",{providedIn:"root",factory:()=>{let e=v(g),n=v(mt);return t=>e.runOutsideAngular(()=>n.handleError(t))}});function Zg(e,n){return Em(e,n)}function Uw(e){return Em(wm,e)}var Rv=(Zg.required=Uw,Zg);function Hw(){return Tr(Se(),Z())}function Tr(e,n){return new m(It(e,n))}var m=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=Hw}return e})();function $w(e){return e instanceof m?e.nativeElement:e}function zw(e){return typeof e=="function"&&e[We]!==void 0}function Mo(e,n){let t=El(e,n?.equal),r=t[We];return t.set=o=>io(r,o),t.update=o=>_l(r,o),t.asReadonly=Gw.bind(t),t}function Gw(){let e=this[We];if(e.readonlyFn===void 0){let n=()=>this();n[We]=e,e.readonlyFn=n}return e.readonlyFn}function Nv(e){return zw(e)&&typeof e.set=="function"}function Ww(){return this._results[Symbol.iterator]()}var su=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new X}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=fb(n);(this._changesDetected=!db(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=Ww};function Ov(e){return(e.flags&128)===128}var kv=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(kv||{}),Fv=new Map,qw=0;function Zw(){return qw++}function Yw(e){Fv.set(e[Xs],e)}function au(e){Fv.delete(e[Xs])}var Yg="__ngContext__";function xr(e,n){Kt(n)?(e[Yg]=n[Xs],Yw(n)):e[Yg]=n}function Pv(e){return jv(e[vo])}function Lv(e){return jv(e[rt])}function jv(e){for(;e!==null&&!Lt(e);)e=e[rt];return e}var cu;function Vv(e){cu=e}function Bv(){if(cu!==void 0)return cu;if(typeof document<"u")return document;throw new x(210,!1)}var id=new A("",{providedIn:"root",factory:()=>Qw}),Qw="ng",sd=new A(""),Jt=new A("",{providedIn:"platform",factory:()=>"unknown"});var ad=new A("",{providedIn:"root",factory:()=>Bv().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Kw="h",Xw="b";var Uv=!1,Jw=new A("",{providedIn:"root",factory:()=>Uv});var cd=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(cd||{}),Ar=new A(""),Qg=new Set;function So(e){Qg.has(e)||(Qg.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Hv=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=eE}return e})();function eE(){return new Hv(Z(),Se())}var lr=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(lr||{}),$v=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})(),tE=[lr.EarlyRead,lr.Write,lr.MixedReadWrite,lr.Read],nE=(()=>{class e{ngZone=v(g);scheduler=v(Ir);errorHandler=v(mt,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){v(Ar,{optional:!0})}execute(){let t=this.sequences.size>0;t&&re(16),this.executing=!0;for(let r of tE)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&re(17)}register(t){let{view:r}=t;r!==void 0?((r[dr]??=[]).push(t),Eo(r),r[L]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(cd.AFTER_NEXT_RENDER,t):t()}static \u0275prov=T({token:e,providedIn:"root",factory:()=>new e})}return e})(),lu=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[dr];n&&(this.view[dr]=n.filter(t=>t!==this))}};function ld(e,n){!n?.injector&&Tb(ld);let t=n?.injector??v(J);return So("NgAfterNextRender"),oE(e,t,n,!0)}function rE(e,n){if(e instanceof Function){let t=[void 0,void 0,void 0,void 0];return t[n]=e,t}else return[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function oE(e,n,t,r){let o=n.get($v);o.impl??=n.get(nE);let i=n.get(Ar,null,{optional:!0}),s=t?.phase??lr.MixedReadWrite,a=t?.manualCleanup!==!0?n.get(_o):null,c=n.get(Hv,null,{optional:!0}),l=new lu(o.impl,rE(e,s),c?.view,r,a,i?.snapshot(null));return o.impl.register(l),l}var iE=()=>null;function zv(e,n,t=!1){return iE(e,n,t)}function Gv(e,n){let t=e.contentQueries;if(t!==null){let r=q(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];Xu(i),a.contentQueries(2,n[s],s)}}}finally{q(r)}}}function uu(e,n,t){Xu(0);let r=q(null);try{n(e,t)}finally{q(r)}}function ud(e,n,t){if($u(n)){let r=q(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{q(r)}}}var vt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(vt||{}),ls;function sE(){if(ls===void 0&&(ls=null,Oe.trustedTypes))try{ls=Oe.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return ls}function ia(e){return sE()?.createHTML(e)||e}var us;function Wv(){if(us===void 0&&(us=null,Oe.trustedTypes))try{us=Oe.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return us}function Kg(e){return Wv()?.createHTML(e)||e}function Xg(e){return Wv()?.createScriptURL(e)||e}var Fs=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Cm})`}};function To(e){return e instanceof Fs?e.changingThisBreaksApplicationSecurity:e}function dd(e,n){let t=aE(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Cm})`)}return t===n}function aE(e){return e instanceof Fs&&e.getTypeName()||null}function cE(e){let n=new fu(e);return lE()?new du(n):n}var du=class{inertDocumentHelper;constructor(n){this.inertDocumentHelper=n}getInertBodyElement(n){n="<body><remove></remove>"+n;try{let t=new window.DOMParser().parseFromString(ia(n),"text/html").body;return t===null?this.inertDocumentHelper.getInertBodyElement(n):(t.firstChild?.remove(),t)}catch{return null}}},fu=class{defaultDoc;inertDocument;constructor(n){this.defaultDoc=n,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(n){let t=this.inertDocument.createElement("template");return t.innerHTML=ia(n),t}};function lE(){try{return!!new window.DOMParser().parseFromString(ia(""),"text/html")}catch{return!1}}var uE=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function qv(e){return e=String(e),e.match(uE)?e:"unsafe:"+e}function Vt(e){let n={};for(let t of e.split(","))n[t]=!0;return n}function xo(...e){let n={};for(let t of e)for(let r in t)t.hasOwnProperty(r)&&(n[r]=!0);return n}var Zv=Vt("area,br,col,hr,img,wbr"),Yv=Vt("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Qv=Vt("rp,rt"),dE=xo(Qv,Yv),fE=xo(Yv,Vt("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),hE=xo(Qv,Vt("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),Jg=xo(Zv,fE,hE,dE),Kv=Vt("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),pE=Vt("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),gE=Vt("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),mE=xo(Kv,pE,gE),vE=Vt("script,style,template"),hu=class{sanitizedSomething=!1;buf=[];sanitizeChildren(n){let t=n.firstChild,r=!0,o=[];for(;t;){if(t.nodeType===Node.ELEMENT_NODE?r=this.startElement(t):t.nodeType===Node.TEXT_NODE?this.chars(t.nodeValue):this.sanitizedSomething=!0,r&&t.firstChild){o.push(t),t=IE(t);continue}for(;t;){t.nodeType===Node.ELEMENT_NODE&&this.endElement(t);let i=DE(t);if(i){t=i;break}t=o.pop()}}return this.buf.join("")}startElement(n){let t=em(n).toLowerCase();if(!Jg.hasOwnProperty(t))return this.sanitizedSomething=!0,!vE.hasOwnProperty(t);this.buf.push("<"),this.buf.push(t);let r=n.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!mE.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let c=i.value;Kv[a]&&(c=qv(c)),this.buf.push(" ",s,'="',tm(c),'"')}return this.buf.push(">"),!0}endElement(n){let t=em(n).toLowerCase();Jg.hasOwnProperty(t)&&!Zv.hasOwnProperty(t)&&(this.buf.push("</"),this.buf.push(t),this.buf.push(">"))}chars(n){this.buf.push(tm(n))}};function yE(e,n){return(e.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function DE(e){let n=e.nextSibling;if(n&&e!==n.previousSibling)throw Xv(n);return n}function IE(e){let n=e.firstChild;if(n&&yE(e,n))throw Xv(n);return n}function em(e){let n=e.nodeName;return typeof n=="string"?n:"FORM"}function Xv(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var CE=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,bE=/([^\#-~ |!])/g;function tm(e){return e.replace(/&/g,"&amp;").replace(CE,function(n){let t=n.charCodeAt(0),r=n.charCodeAt(1);return"&#"+((t-55296)*1024+(r-56320)+65536)+";"}).replace(bE,function(n){return"&#"+n.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var ds;function wE(e,n){let t=null;try{ds=ds||cE(e);let r=n?String(n):"";t=ds.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=t.innerHTML,t=ds.getInertBodyElement(r)}while(r!==i);let a=new hu().sanitizeChildren(nm(t)||t);return ia(a)}finally{if(t){let r=nm(t)||t;for(;r.firstChild;)r.firstChild.remove()}}}function nm(e){return"content"in e&&EE(e)?e.content:null}function EE(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var sa=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(sa||{});function QB(e){let n=fd();return n?Kg(n.sanitize(sa.HTML,e)||""):dd(e,"HTML")?Kg(To(e)):wE(Bv(),bn(e))}function _E(e){let n=fd();return n?n.sanitize(sa.URL,e)||"":dd(e,"URL")?To(e):qv(bn(e))}function ME(e){let n=fd();if(n)return Xg(n.sanitize(sa.RESOURCE_URL,e)||"");if(dd(e,"ResourceURL"))return Xg(To(e));throw new x(904,!1)}function SE(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?ME:_E}function Jv(e,n,t){return SE(n,t)(e)}function fd(){let e=Z();return e&&e[Ot].sanitizer}var TE=/^>|^->|<!--|-->|--!>|<!-$/g,xE=/(<|>)/g,AE="\u200B$1\u200B";function RE(e){return e.replace(TE,n=>n.replace(xE,AE))}function ey(e){return e instanceof Function?e():e}function NE(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var ty="ng-template";function OE(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&NE(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(hd(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function hd(e){return e.type===4&&e.value!==ty}function kE(e,n,t){let r=e.type===4&&!t?ty:e.value;return n===r}function FE(e,n,t){let r=4,o=e.attrs,i=o!==null?jE(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!nt(r)&&!nt(c))return!1;if(s&&nt(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!kE(e,c,t)||c===""&&n.length===1){if(nt(r))return!1;s=!0}}else if(r&8){if(o===null||!OE(e,o,c,t)){if(nt(r))return!1;s=!0}}else{let l=n[++a],d=PE(c,o,hd(e),t);if(d===-1){if(nt(r))return!1;s=!0;continue}if(l!==""){let h;if(d>i?h="":h=o[d+1].toLowerCase(),r&2&&l!==h){if(nt(r))return!1;s=!0}}}}return nt(r)||s}function nt(e){return(e&1)===0}function PE(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return VE(n,e)}function ny(e,n,t=!1){for(let r=0;r<n.length;r++)if(FE(e,n[r],t))return!0;return!1}function LE(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function jE(e){for(let n=0;n<e.length;n++){let t=e[n];if(hv(t))return n}return e.length}function VE(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function BE(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function rm(e,n){return e?":not("+n.trim()+")":n}function UE(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!nt(s)&&(n+=rm(i,o),o=""),r=s,i=i||!nt(r);t++}return o!==""&&(n+=rm(i,o)),n}function HE(e){return e.map(UE).join(",")}function $E(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!nt(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var bt={};function zE(e,n){return e.createText(n)}function GE(e,n,t){e.setValue(n,t)}function WE(e,n){return e.createComment(RE(n))}function ry(e,n,t){return e.createElement(n,t)}function Ps(e,n,t,r,o){e.insertBefore(n,t,r,o)}function oy(e,n,t){e.appendChild(n,t)}function om(e,n,t,r,o){r!==null?Ps(e,n,t,r,o):oy(e,n,t)}function qE(e,n,t){e.removeChild(null,n,t)}function ZE(e,n,t){e.setAttribute(n,"style",t)}function YE(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function iy(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&dw(e,n,r),o!==null&&YE(e,n,o),i!==null&&ZE(e,n,i)}function pd(e,n,t,r,o,i,s,a,c,l,d){let h=Ke+r,p=h+o,f=QE(h,p),y=typeof l=="function"?l():l;return f[G]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,h),bindingStartIndex:h,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:y,incompleteFirstPass:!1,ssrId:d}}function QE(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:bt);return t}function KE(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=pd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function gd(e,n,t,r,o,i,s,a,c,l,d){let h=n.blueprint.slice();return h[Pt]=o,h[L]=r|4|128|8|64|1024,(l!==null||e&&e[L]&2048)&&(h[L]|=2048),Xm(h),h[_e]=h[Mr]=e,h[ke]=t,h[Ot]=s||e&&e[Ot],h[ue]=a||e&&e[ue],h[mr]=c||e&&e[mr]||null,h[Pe]=i,h[Xs]=Zw(),h[mo]=d,h[zm]=l,h[Qe]=n.type==2?e[Qe]:h,h}function XE(e,n,t){let r=It(n,e),o=KE(t),i=e[Ot].rendererFactory,s=md(e,gd(e,o,null,sy(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function sy(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function ay(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function md(e,n){return e[vo]?e[Og][rt]=n:e[vo]=n,e[Og]=n,n}function aa(e=1){cy(fe(),Z(),On()+e,!1)}function cy(e,n,t,r){if(!r)if((n[L]&3)===3){let i=e.preOrderCheckHooks;i!==null&&ms(n,i,t)}else{let i=e.preOrderHooks;i!==null&&vs(n,i,0,t)}Sn(t)}var ca=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ca||{});function pu(e,n,t,r){let o=q(null);try{let[i,s,a]=e.inputs[t],c=null;(s&ca.SignalBased)!==0&&(c=n[i][We]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Wm(n,c,i,r)}finally{q(o)}}function ly(e,n,t,r,o){let i=On(),s=r&2;try{Sn(-1),s&&n.length>Ke&&cy(e,n,Ke,!1),re(s?2:0,o),t(r,o)}finally{Sn(i),re(s?3:1,o)}}function la(e,n,t){o_(e,n,t),(t.flags&64)===64&&i_(e,n,t)}function vd(e,n,t=It){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function JE(e,n,t,r){let i=r.get(Jw,Uv)||t===vt.ShadowDom,s=e.selectRootElement(n,i);return e_(s),s}function e_(e){t_(e)}var t_=()=>null;function n_(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function yd(e,n,t,r,o,i,s,a){if(!a&&Id(n,e,t,r,o)){Sr(n)&&r_(t,n.index);return}if(n.type&3){let c=It(n,t);r=n_(r),o=s!=null?s(o,n.value||"",r):o,i.setProperty(c,r,o)}else n.type&12}function r_(e,n){let t=gt(n,e);t[L]&16||(t[L]|=64)}function o_(e,n,t){let r=t.directiveStart,o=t.directiveEnd;Sr(t)&&XE(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||Rs(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=yo(n,e,s,t);if(xr(c,n),i!==null&&l_(n,s-r,c,a,t,i),ht(a)){let l=gt(t.index,n);l[ke]=yo(n,e,s,t)}}}function i_(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=nw();try{Sn(i);for(let a=r;a<o;a++){let c=e.data[a],l=n[a];Jl(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&s_(c,l)}}finally{Sn(-1),Jl(s)}}function s_(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function Dd(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];ny(n,i.selectors,!1)&&(r??=[],ht(i)?r.unshift(i):r.push(i))}return r}function a_(e,n,t,r,o,i){let s=It(e,n);c_(n[ue],s,i,e.value,t,r,o)}function c_(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?bn(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function l_(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],l=s[a+1];pu(r,t,c,l)}}function u_(e,n){let t=e[mr],r=t?t.get(mt,null):null;r&&r.handleError(n)}function Id(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let l=s[c],d=s[c+1],h=n.data[l];pu(h,t[l],d,o),a=!0}if(i)for(let c of i){let l=t[c],d=n.data[c];pu(d,l,r,o),a=!0}return a}function d_(e,n){let t=gt(n,e),r=t[G];f_(r,t);let o=t[Pt];o!==null&&t[mo]===null&&(t[mo]=zv(o,t[mr])),re(18),Cd(r,t,t[ke]),re(19,t[ke])}function f_(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function Cd(e,n,t){Ju(n);try{let r=e.viewQuery;r!==null&&uu(1,r,t);let o=e.template;o!==null&&ly(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[kt]?.finishViewCreation(e),e.staticContentQueries&&Gv(e,n),e.staticViewQueries&&uu(2,e.viewQuery,t);let i=e.components;i!==null&&h_(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[L]&=-5,ed()}}function h_(e,n){for(let t=0;t<n.length;t++)d_(e,n[t])}function uy(e,n,t,r){let o=q(null);try{let i=n.tView,a=e[L]&4096?4096:16,c=gd(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[n.index];c[En]=l;let d=e[kt];return d!==null&&(c[kt]=d.createEmbeddedView(i)),Cd(i,c,t),c}finally{q(o)}}function gu(e,n){return!n||n.firstChild===null||Ov(e)}var p_;function bd(e,n){return p_(e,n)}var Ft=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ft||{});function wd(e){return(e.flags&32)===32}function ur(e,n,t,r,o){if(r!=null){let i,s=!1;Lt(r)?i=r:Kt(r)&&(s=!0,r=r[Pt]);let a=pt(r);e===0&&t!==null?o==null?oy(n,t,a):Ps(n,t,a,o||null,!0):e===1&&t!==null?Ps(n,t,a,o||null,!0):e===2?qE(n,a,s):e===3&&n.destroyNode(a),i!=null&&E_(n,e,i,t,o)}}function g_(e,n){dy(e,n),n[Pt]=null,n[Pe]=null}function m_(e,n,t,r,o,i){r[Pt]=o,r[Pe]=n,da(e,r,t,1,o,i)}function dy(e,n){n[Ot].changeDetectionScheduler?.notify(9),da(e,n,n[ue],2,null,null)}function v_(e){let n=e[vo];if(!n)return kl(e[G],e);for(;n;){let t=null;if(Kt(n))t=n[vo];else{let r=n[Ve];r&&(t=r)}if(!t){for(;n&&!n[rt]&&n!==e;)Kt(n)&&kl(n[G],n),n=n[_e];n===null&&(n=e),Kt(n)&&kl(n[G],n),t=n&&n[rt]}n=t}}function Ed(e,n){let t=e[vr],r=t.indexOf(n);t.splice(r,1)}function fy(e,n){if(wo(n))return;let t=n[ue];t.destroyNode&&da(e,n,t,3,null,null),v_(n)}function kl(e,n){if(wo(n))return;let t=q(null);try{n[L]&=-129,n[L]|=256,n[Ye]&&Cl(n[Ye]),D_(e,n),y_(e,n),n[G].type===1&&n[ue].destroy();let r=n[En];if(r!==null&&Lt(n[_e])){r!==n[_e]&&Ed(r,n);let o=n[kt];o!==null&&o.detachView(e)}au(n)}finally{q(t)}}function y_(e,n){let t=e.cleanup,r=n[_s];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[_s]=null);let o=n[Qt];if(o!==null){n[Qt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[Ms];if(i!==null){n[Ms]=null;for(let s of i)s.destroy()}}function D_(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof Tn)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];re(4,a,c);try{c.call(a)}finally{re(5,a,c)}}else{re(4,o,i);try{i.call(o)}finally{re(5,o,i)}}}}}function hy(e,n,t){return I_(e,n.parent,t)}function I_(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[Pt];if(Sr(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===vt.None||o===vt.Emulated)return null}return It(r,t)}function py(e,n,t){return b_(e,n,t)}function C_(e,n,t){return e.type&40?It(e,t):null}var b_=C_,im;function ua(e,n,t,r){let o=hy(e,r,n),i=n[ue],s=r.parent||n[Pe],a=py(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)om(i,o,t[c],a,!1);else om(i,o,t,a,!1);im!==void 0&&im(i,r,n,t,o)}function uo(e,n){if(n!==null){let t=n.type;if(t&3)return It(n,e);if(t&4)return mu(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return uo(e,r);{let o=e[n.index];return Lt(o)?mu(-1,o):pt(o)}}else{if(t&128)return uo(e,n.next);if(t&32)return bd(n,e)()||pt(e[n.index]);{let r=gy(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Mn(e[Qe]);return uo(o,r)}else return uo(e,n.next)}}}return null}function gy(e,n){if(n!==null){let r=e[Qe][Pe],o=n.projection;return r.projection[o]}return null}function mu(e,n){let t=Ve+e+1;if(t<n.length){let r=n[t],o=r[G].firstChild;if(o!==null)return uo(r,o)}return n[_n]}function _d(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&xr(pt(a),r),t.flags|=2),!wd(t))if(c&8)_d(e,n,t.child,r,o,i,!1),ur(n,e,o,a,i);else if(c&32){let l=bd(t,r),d;for(;d=l();)ur(n,e,o,d,i);ur(n,e,o,a,i)}else c&16?my(e,n,r,t,o,i):ur(n,e,o,a,i);t=s?t.projectionNext:t.next}}function da(e,n,t,r,o,i){_d(t,r,e.firstChild,n,o,i,!1)}function w_(e,n,t){let r=n[ue],o=hy(e,t,n),i=t.parent||n[Pe],s=py(i,t,n);my(r,0,n,t,o,s)}function my(e,n,t,r,o,i){let s=t[Qe],c=s[Pe].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let d=c[l];ur(n,e,o,d,i)}else{let l=c,d=s[_e];Ov(r)&&(l.flags|=128),_d(e,n,l,d,o,i,!0)}}function E_(e,n,t,r,o){let i=t[_n],s=pt(t);i!==s&&ur(n,e,r,i,o);for(let a=Ve;a<t.length;a++){let c=t[a];da(c[G],c,e,n,r,i)}}function __(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:Ft.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Ft.Important),e.setStyle(t,r,o,i))}}function Ls(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(pt(i)),Lt(i)&&M_(i,r);let s=t.type;if(s&8)Ls(e,n,t.child,r);else if(s&32){let a=bd(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=gy(n,t);if(Array.isArray(a))r.push(...a);else{let c=Mn(n[Qe]);Ls(c[G],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function M_(e,n){for(let t=Ve;t<e.length;t++){let r=e[t],o=r[G].firstChild;o!==null&&Ls(r[G],r,o,n)}e[_n]!==e[Pt]&&n.push(e[_n])}function vy(e){if(e[dr]!==null){for(let n of e[dr])n.impl.addSequence(n);e[dr].length=0}}var yy=[];function S_(e){return e[Ye]??T_(e)}function T_(e){let n=yy.pop()??Object.create(A_);return n.lView=e,n}function x_(e){e.lView[Ye]!==e&&(e.lView=null,yy.push(e))}var A_=P(I({},ro),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Eo(e.lView)},consumerOnSignalRead(){this.lView[Ye]=this}});function R_(e){let n=e[Ye]??Object.create(N_);return n.lView=e,n}var N_=P(I({},ro),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Mn(e.lView);for(;n&&!Dy(n[G]);)n=Mn(n);n&&Jm(n)},consumerOnSignalRead(){this.lView[Ye]=this}});function Dy(e){return e.type!==2}function Iy(e){if(e[Ms]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[Ms])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[L]&8192)}}var O_=100;function Cy(e,n=!0,t=0){let o=e[Ot].rendererFactory,i=!1;i||o.begin?.();try{k_(e,t)}catch(s){throw n&&u_(e,s),s}finally{i||o.end?.()}}function k_(e,n){let t=rv();try{Fg(!0),vu(e,n);let r=0;for(;ea(e);){if(r===O_)throw new x(103,!1);r++,vu(e,1)}}finally{Fg(t)}}function F_(e,n,t,r){if(wo(n))return;let o=n[L],i=!1,s=!1;Ju(n);let a=!0,c=null,l=null;i||(Dy(e)?(l=S_(n),c=rs(l)):gl()===null?(a=!1,l=R_(n),c=rs(l)):n[Ye]&&(Cl(n[Ye]),n[Ye]=null));try{Xm(n),Jb(e.bindingStartIndex),t!==null&&ly(e,n,t,2,r);let d=(o&3)===3;if(!i)if(d){let f=e.preOrderCheckHooks;f!==null&&ms(n,f,null)}else{let f=e.preOrderHooks;f!==null&&vs(n,f,0,null),Nl(n,0)}if(s||P_(n),Iy(n),by(n,0),e.contentQueries!==null&&Gv(e,n),!i)if(d){let f=e.contentCheckHooks;f!==null&&ms(n,f)}else{let f=e.contentHooks;f!==null&&vs(n,f,1),Nl(n,1)}j_(e,n);let h=e.components;h!==null&&Ey(n,h,0);let p=e.viewQuery;if(p!==null&&uu(2,p,r),!i)if(d){let f=e.viewCheckHooks;f!==null&&ms(n,f)}else{let f=e.viewHooks;f!==null&&vs(n,f,2),Nl(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[Rl]){for(let f of n[Rl])f();n[Rl]=null}i||(vy(n),n[L]&=-73)}catch(d){throw i||Eo(n),d}finally{l!==null&&(Dl(l,c),a&&x_(l)),ed()}}function by(e,n){for(let t=Pv(e);t!==null;t=Lv(t))for(let r=Ve;r<t.length;r++){let o=t[r];wy(o,n)}}function P_(e){for(let n=Pv(e);n!==null;n=Lv(n)){if(!(n[L]&2))continue;let t=n[vr];for(let r=0;r<t.length;r++){let o=t[r];Jm(o)}}}function L_(e,n,t){re(18);let r=gt(n,e);wy(r,t),re(19,r[ke])}function wy(e,n){zu(e)&&vu(e,n)}function vu(e,n){let r=e[G],o=e[L],i=e[Ye],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&Il(i)),s||=!1,i&&(i.dirty=!1),e[L]&=-9217,s)F_(r,e,r.template,e[ke]);else if(o&8192){Iy(e),by(e,1);let a=r.components;a!==null&&Ey(e,a,1),vy(e)}}function Ey(e,n,t){for(let r=0;r<n.length;r++)L_(e,n[r],t)}function j_(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Sn(~o);else{let i=o,s=t[++r],a=t[++r];tw(s,i);let c=n[i];re(24,c),a(2,c),re(25,c)}}}finally{Sn(-1)}}function Md(e,n){let t=rv()?64:1088;for(e[Ot].changeDetectionScheduler?.notify(n);e;){e[L]|=t;let r=Mn(e);if(Ts(e)&&!r)return e;e=r}return null}function _y(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function My(e,n,t,r=!0){let o=n[G];if(V_(o,n,e,t),r){let s=mu(t,e),a=n[ue],c=a.parentNode(e[_n]);c!==null&&m_(o,e[Pe],a,n,c,s)}let i=n[mo];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function yu(e,n){if(e.length<=Ve)return;let t=Ve+n,r=e[t];if(r){let o=r[En];o!==null&&o!==e&&Ed(o,r),n>0&&(e[t-1][rt]=r[rt]);let i=ws(e,Ve+n);g_(r[G],r);let s=i[kt];s!==null&&s.detachView(i[G]),r[_e]=null,r[rt]=null,r[L]&=-129}return r}function V_(e,n,t,r){let o=Ve+r,i=t.length;r>0&&(t[o-1][rt]=n),r<i-Ve?(n[rt]=t[o],Fm(t,Ve+r,n)):(t.push(n),n[rt]=null),n[_e]=t;let s=n[En];s!==null&&t!==s&&Sy(s,n);let a=n[kt];a!==null&&a.insertView(e),Kl(n),n[L]|=128}function Sy(e,n){let t=e[vr],r=n[_e];if(Kt(r))e[L]|=2;else{let o=r[_e][Qe];n[Qe]!==o&&(e[L]|=2)}t===null?e[vr]=[n]:t.push(n)}var Do=class{_lView;_cdRefInjectingView;notifyErrorHandler;_appRef=null;_attachedToViewContainer=!1;get rootNodes(){let n=this._lView,t=n[G];return Ls(t,n,t.firstChild,[])}constructor(n,t,r=!0){this._lView=n,this._cdRefInjectingView=t,this.notifyErrorHandler=r}get context(){return this._lView[ke]}set context(n){this._lView[ke]=n}get destroyed(){return wo(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[_e];if(Lt(n)){let t=n[Ss],r=t?t.indexOf(this):-1;r>-1&&(yu(n,r),ws(t,r))}this._attachedToViewContainer=!1}fy(this._lView[G],this._lView)}onDestroy(n){ev(this._lView,n)}markForCheck(){Md(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[L]&=-129}reattach(){Kl(this._lView),this._lView[L]|=128}detectChanges(){this._lView[L]|=1024,Cy(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new x(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=Ts(this._lView),t=this._lView[En];t!==null&&!n&&Ed(t,this._lView),dy(this._lView[G],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new x(902,!1);this._appRef=n;let t=Ts(this._lView),r=this._lView[En];r!==null&&!t&&Sy(r,this._lView),Kl(this._lView)}};var yt=(()=>{class e{static __NG_ELEMENT_ID__=H_}return e})(),B_=yt,U_=class extends B_{_declarationLView;_declarationTContainer;elementRef;constructor(n,t,r){super(),this._declarationLView=n,this._declarationTContainer=t,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(n,t){return this.createEmbeddedViewImpl(n,t)}createEmbeddedViewImpl(n,t,r){let o=uy(this._declarationLView,this._declarationTContainer,n,{embeddedViewInjector:t,dehydratedView:r});return new Do(o)}};function H_(){return Sd(Se(),Z())}function Sd(e,n){return e.type&4?new U_(n,e,Tr(e,n)):null}function Ao(e,n,t,r,o){let i=e.data[n];if(i===null)i=$_(e,n,t,r,o),ew()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=Qb();i.injectorIndex=s===null?-1:s.injectorIndex}return Nn(i,!0),i}function $_(e,n,t,r,o){let i=nv(),s=Qu(),a=s?i:i&&i.parent,c=e.data[n]=G_(e,a,t,n,r,o);return z_(e,c,i,s),c}function z_(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function G_(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return tv()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var JB=new RegExp(`^(\\d+)*(${Xw}|${Kw})*(.*)`);var W_=()=>null;function Du(e,n){return W_(e,n)}var q_=class{},Ty=class{},Iu=class{resolveComponentFactory(n){throw Error(`No component factory found for ${Ne(n)}.`)}},fa=class{static NULL=new Iu},Cr=class{},en=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Z_()}return e})();function Z_(){let e=Z(),n=Se(),t=gt(n.index,e);return(Kt(t)?t:e)[ue]}var Y_=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>null})}return e})();var Fl={},Cu=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){r=Zs(r);let o=this.injector.get(n,Fl,r);return o!==Fl||t===Fl?o:this.parentInjector.get(n,t,r)}};function bu(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=_g(o,a);else if(i==2){let c=a,l=n[++s];r=_g(r,c+": "+l+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function u(e,n=H.Default){let t=Z();if(t===null)return R(e,n);let r=Se();return Iv(r,t,Ee(e),n)}function xy(){let e="invalid";throw new Error(e)}function Td(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a,c=null,l=null,d=K_(s);d===null?a=s:[a,c,l]=d,eM(e,n,t,a,i,c,l)}i!==null&&r!==null&&Q_(t,r,i)}function Q_(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new x(-301,!1);r.push(n[o],i)}}function K_(e){let n=null,t=!1;for(let s=0;s<e.length;s++){let a=e[s];if(s===0&&ht(a)&&(n=a),a.findHostDirectiveDefs!==null){t=!0;break}}if(!t)return null;let r=null,o=null,i=null;for(let s of e)s.findHostDirectiveDefs!==null&&(r??=[],o??=new Map,i??=new Map,X_(s,r,i,o)),s===n&&(r??=[],r.push(s));return r!==null?(r.push(...n===null?e:e.slice(1)),[r,o,i]):null}function X_(e,n,t,r){let o=n.length;e.findHostDirectiveDefs(e,n,r),t.set(e,[o,n.length-1])}function J_(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function eM(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let p=0;p<a;p++){let f=r[p];!c&&ht(f)&&(c=!0,J_(e,t,p)),tu(Rs(t,n),e,f.type)}sM(t,e.data.length,a);for(let p=0;p<a;p++){let f=r[p];f.providersResolver&&f.providersResolver(f)}let l=!1,d=!1,h=ay(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let p=0;p<a;p++){let f=r[p];if(t.mergedAttrs=Dr(t.mergedAttrs,f.hostAttrs),nM(e,t,n,h,f),iM(h,f,o),s!==null&&s.has(f)){let[S,N]=s.get(f);t.directiveToIndex.set(f.type,[h,S+t.directiveStart,N+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,h);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let y=f.type.prototype;!l&&(y.ngOnChanges||y.ngOnInit||y.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),l=!0),!d&&(y.ngOnChanges||y.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),d=!0),h++}tM(e,t,i)}function tM(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))sm(0,n,o,r),sm(1,n,o,r),cm(n,r,!1);else{let i=t.get(o);am(0,n,i,r),am(1,n,i,r),cm(n,r,!0)}}}function sm(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),Ay(n,i)}}function am(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Ay(n,s)}}function Ay(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function cm(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||hd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let l=o[c];for(let d of l)if(d===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let l=i[c];for(let d=0;d<l.length;d+=2)if(l[d]===n){s??=[],s.push(l[d+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function nM(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=pr(o.type,!0)),s=new Tn(i,ht(o),u);e.blueprint[r]=s,t[r]=s,rM(e,n,r,ay(e,t,o.hostVars,bt),o)}function rM(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;oM(s)!=a&&s.push(a),s.push(t,r,i)}}function oM(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function iM(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;ht(n)&&(t[""]=e)}}function sM(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function Ry(e,n,t,r,o,i,s,a){let c=n.consts,l=yr(c,s),d=Ao(n,e,2,r,l);return i&&Td(n,t,d,yr(c,a),o),d.mergedAttrs=Dr(d.mergedAttrs,d.attrs),d.attrs!==null&&bu(d,d.attrs,!1),d.mergedAttrs!==null&&bu(d,d.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,d),d}function Ny(e,n){td(e,n),$u(n)&&e.queries.elementEnd(n)}var js=class extends fa{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=Xt(n);return new xn(t,this.ngModule)}};function aM(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&ca.SignalBased)!==0};return o&&(i.transform=o),i})}function cM(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function lM(e,n,t){let r=n instanceof ie?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Cu(t,r):t}function uM(e){let n=e.get(Cr,null);if(n===null)throw new x(407,!1);let t=e.get(Y_,null),r=e.get(Ir,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r}}function dM(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return ry(n,t,t==="svg"?jb:t==="math"?Vb:null)}var xn=class extends Ty{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=aM(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=cM(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=HE(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o){re(22);let i=q(null);try{let s=this.componentDef,a=r?["ng-version","19.2.7"]:$E(this.componentDef.selectors[0]),c=pd(0,null,null,1,0,null,null,null,null,[a],null),l=lM(s,o||this.ngModule,n),d=uM(l),h=d.rendererFactory.createRenderer(null,s),p=r?JE(h,r,s.encapsulation,l):dM(s,h),f=gd(null,c,null,512|sy(s),null,null,d,h,l,null,zv(p,l,!0));f[Ke]=p,Ju(f);let y=null;try{let S=Ry(Ke,c,f,"#host",()=>[this.componentDef],!0,0);p&&(iy(h,p,S),xr(p,f)),la(c,f,S),ud(c,S,f),Ny(c,S),t!==void 0&&fM(S,this.ngContentSelectors,t),y=gt(S.index,f),f[ke]=y[ke],Cd(c,f,null)}catch(S){throw y!==null&&au(y),au(f),S}finally{re(23),ed()}return new wu(this.componentType,f)}finally{q(i)}}},wu=class extends q_{_rootLView;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t){super(),this._rootLView=t,this._tNode=Km(t[G],Ke),this.location=Tr(this._tNode,t),this.instance=gt(this._tNode.index,t)[ke],this.hostView=this.changeDetectorRef=new Do(t,void 0,!1),this.componentType=n}setInput(n,t){let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=Id(r,o[G],o,n,t);this.previousInputValues.set(n,t);let s=gt(r.index,o);Md(s,1)}get injector(){return new Cn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function fM(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Ue=(()=>{class e{static __NG_ELEMENT_ID__=hM}return e})();function hM(){let e=Se();return ky(e,Z())}var pM=Ue,Oy=class extends pM{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return Tr(this._hostTNode,this._hostLView)}get injector(){return new Cn(this._hostTNode,this._hostLView)}get parentInjector(){let n=nd(this._hostTNode,this._hostLView);if(pv(n)){let t=As(n,this._hostLView),r=xs(n),o=t[G].data[r+8];return new Cn(o,t)}else return new Cn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=lm(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-Ve}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Du(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,gu(this._hostTNode,s)),a}createComponent(n,t,r,o,i){let s=n&&!lo(n),a;if(s)a=t;else{let y=t||{};a=y.index,r=y.injector,o=y.projectableNodes,i=y.environmentInjector||y.ngModuleRef}let c=s?n:new xn(Xt(n)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let S=(s?l:this.parentInjector).get(ie,null);S&&(i=S)}let d=Xt(c.componentType??{}),h=Du(this._lContainer,d?.id??null),p=h?.firstChild??null,f=c.create(l,o,p,i);return this.insertImpl(f.hostView,a,gu(this._hostTNode,h)),f}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(Ub(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[_e],l=new Oy(c,c[Pe],c[_e]);l.detach(l.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return My(s,o,i,r),n.attachToViewContainerRef(),Fm(Pl(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=lm(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=yu(this._lContainer,t);r&&(ws(Pl(this._lContainer),t),fy(r[G],r))}detach(n){let t=this._adjustIndex(n,-1),r=yu(this._lContainer,t);return r&&ws(Pl(this._lContainer),t)!=null?new Do(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function lm(e){return e[Ss]}function Pl(e){return e[Ss]||(e[Ss]=[])}function ky(e,n){let t,r=n[e.index];return Lt(r)?t=r:(t=_y(r,n,null,e),n[e.index]=t,md(n,t)),mM(t,n,e,r),new Oy(t,e,n)}function gM(e,n){let t=e[ue],r=t.createComment(""),o=It(n,e),i=t.parentNode(o);return Ps(t,i,r,t.nextSibling(o),!1),r}var mM=DM,vM=()=>!1;function yM(e,n,t){return vM(e,n,t)}function DM(e,n,t,r){if(e[_n])return;let o;t.type&8?o=pt(r):o=gM(n,t),e[_n]=o}var Eu=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},_u=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)xd(n,t).matches!==null&&this.queries[t].setDirty()}},Vs=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=SM(n):this.predicate=n}},Mu=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},Su=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,IM(t,i)),this.matchTNodeWithReadOption(n,t,ys(t,n,i,!1,!1))}else r===yt?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,ys(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===m||o===Ue||o===yt&&t.type&4)this.addMatch(t.index,-2);else{let i=ys(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function IM(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function CM(e,n){return e.type&11?Tr(e,n):e.type&4?Sd(e,n):null}function bM(e,n,t,r){return t===-1?CM(n,e):t===-2?wM(e,n,r):yo(e,e[G],t,n)}function wM(e,n,t){if(t===m)return Tr(n,e);if(t===yt)return Sd(n,e);if(t===Ue)return ky(n,e)}function Fy(e,n,t,r){let o=n[kt].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let l=s[c];if(l<0)a.push(null);else{let d=i[l];a.push(bM(n,d,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function Tu(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Fy(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let l=i[a+1],d=n[-c];for(let h=Ve;h<d.length;h++){let p=d[h];p[En]===p[_e]&&Tu(p[G],p,l,r)}if(d[vr]!==null){let h=d[vr];for(let p=0;p<h.length;p++){let f=h[p];Tu(f[G],f,l,r)}}}}}return r}function EM(e,n){return e[kt].queries[n].queryList}function Py(e,n,t){let r=new su((t&4)===4);return zb(e,n,r,r.destroy),(n[kt]??=new _u).queries.push(new Eu(r))-1}function _M(e,n,t){let r=fe();return r.firstCreatePass&&(Ly(r,new Vs(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Py(r,Z(),n)}function MM(e,n,t,r){let o=fe();if(o.firstCreatePass){let i=Se();Ly(o,new Vs(n,t,r),i.index),TM(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Py(o,Z(),t)}function SM(e){return e.split(",").map(n=>n.trim())}function Ly(e,n,t){e.queries===null&&(e.queries=new Mu),e.queries.track(new Su(n,t))}function TM(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function xd(e,n){return e.queries.getByIndex(n)}function xM(e,n){let t=e[G],r=xd(t,n);return r.crossesNgTemplate?Tu(t,e,n,[]):Fy(t,e,r,n)}function AM(e){let n=[],t=new Map;function r(o){let i=t.get(o);if(!i){let s=e(o);t.set(o,i=s.then(kM))}return i}return Bs.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(l=>{o.template=l}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let l=o.styles.length,d=o.styleUrls;o.styleUrls.forEach((h,p)=>{a.push(""),s.push(r(h).then(f=>{a[l+p]=f,d.splice(d.indexOf(h),1),d.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(l=>{a.push(l),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>FM(i));n.push(c)}),NM(),Promise.all(n).then(()=>{})}var Bs=new Map,RM=new Set;function NM(){let e=Bs;return Bs=new Map,e}function OM(){return Bs.size===0}function kM(e){return typeof e=="string"?e:e.text()}function FM(e){RM.delete(e)}var br=class{},Ad=class{};var Us=class extends br{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new js(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=jm(n);this._bootstrapComponents=ey(i.bootstrap),this._r3Injector=Ev(n,t,[{provide:br,useValue:this},{provide:fa,useValue:this.componentFactoryResolver},...r],Ne(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},Hs=class extends Ad{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new Us(this.moduleType,n,[])}};function PM(e,n,t){return new Us(e,n,t,!1)}var xu=class extends br{injector;componentFactoryResolver=new js(this);instance=null;constructor(n){super();let t=new go([...n.providers,{provide:br,useValue:this},{provide:fa,useValue:this.componentFactoryResolver}],n.parent||Ks(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Ro(e,n,t=null){return new xu({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var LM=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Vm(!1,t.type),o=r.length>0?Ro([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=T({token:e,providedIn:"environment",factory:()=>new e(R(ie))})}return e})();function C(e){return Er(()=>{let n=jy(e),t=P(I({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===kv.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(LM).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||vt.Emulated,styles:e.styles||Ze,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&So("NgStandalone"),Vy(t);let r=e.dependencies;return t.directiveDefs=um(r,!1),t.pipeDefs=um(r,!0),t.id=HM(t),t})}function jM(e){return Xt(e)||mb(e)}function VM(e){return e!==null}function xe(e){return Er(()=>({type:e.type,bootstrap:e.bootstrap||Ze,declarations:e.declarations||Ze,imports:e.imports||Ze,exports:e.exports||Ze,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function BM(e,n){if(e==null)return wn;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ca.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function UM(e){if(e==null)return wn;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function V(e){return Er(()=>{let n=jy(e);return Vy(n),n})}function jy(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||wn,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ze,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:BM(e.inputs,n),outputs:UM(e.outputs),debugInfo:null}}function Vy(e){e.features?.forEach(n=>n(e))}function um(e,n){if(!e)return null;let t=n?vb:jM;return()=>(typeof e=="function"?e():e).map(r=>t(r)).filter(VM)}function HM(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function $M(e){return Object.getPrototypeOf(e.prototype).constructor}function ee(e){let n=$M(e.type),t=!0,r=[e];for(;n;){let o;if(ht(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new x(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Ll(e.inputs),s.declaredInputs=Ll(e.declaredInputs),s.outputs=Ll(e.outputs);let a=o.hostBindings;a&&ZM(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&WM(e,c),l&&qM(e,l),zM(e,o),z0(e.outputs,o.outputs),ht(o)&&o.data.animation){let d=e.data;d.animation=(d.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===ee&&(t=!1)}}n=Object.getPrototypeOf(n)}GM(r)}function zM(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function GM(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=Dr(o.hostAttrs,t=Dr(t,o.hostAttrs))}}function Ll(e){return e===wn?{}:e===Ze?[]:e}function WM(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function qM(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function ZM(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function By(e){return QM(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function YM(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function QM(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function KM(e,n,t){return e[n]=t}function XM(e,n){return e[n]}function An(e,n,t){let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function JM(e,n,t,r){let o=An(e,n,t);return An(e,n+1,r)||o}function eS(e,n,t,r,o,i,s,a,c){let l=n.consts,d=Ao(n,e,4,s||null,a||null);qu()&&Td(n,t,d,yr(l,c),Dd),d.mergedAttrs=Dr(d.mergedAttrs,d.attrs),td(n,d);let h=d.tView=pd(2,d,r,o,i,n.directiveRegistry,n.pipeRegistry,null,n.schemas,l,null);return n.queries!==null&&(n.queries.template(n,d),h.queries=n.queries.embeddedTView(d)),d}function Uy(e,n,t,r,o,i,s,a,c,l){let d=t+Ke,h=n.firstCreatePass?eS(d,n,e,r,o,i,s,a,c):n.data[d];Nn(h,!1);let p=tS(n,e,h,t);ra()&&ua(n,e,p,h),xr(p,e);let f=_y(p,e,p,h);return e[d]=f,md(e,f),yM(f,h,e),Js(h)&&la(n,e,h),c!=null&&vd(e,h,l),h}function No(e,n,t,r,o,i,s,a){let c=Z(),l=fe(),d=yr(l.consts,i);return Uy(c,l,e,n,t,r,o,d,s,a),No}var tS=nS;function nS(e,n,t,r){return oa(!0),n[ue].createComment("")}var Hy=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Rd=new A(""),Oo=new A(""),ha=(()=>{class e{_ngZone;registry;_isZoneStable=!0;_callbacks=[];taskTrackingZone=null;constructor(t,r,o){this._ngZone=t,this.registry=r,Nd||(rS(o),o.addToWindow(r)),this._watchAngularEvents(),t.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let t=this._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb()}});else{let t=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(t)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(t=>({source:t.source,creationLocation:t.creationLocation,data:t.data})):[]}addCallback(t,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),t()},r)),this._callbacks.push({doneCb:t,timeoutId:i,updateCb:o})}whenStable(t,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(t,r,o),this._runCallbacksIfReady()}registerApplication(t){this.registry.registerApplication(t,this)}unregisterApplication(t){this.registry.unregisterApplication(t)}findProviders(t,r,o){return[]}static \u0275fac=function(r){return new(r||e)(R(g),R(pa),R(Oo))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),pa=(()=>{class e{_applications=new Map;registerApplication(t,r){this._applications.set(t,r)}unregisterApplication(t){this._applications.delete(t)}unregisterAllApplications(){this._applications.clear()}getTestability(t){return this._applications.get(t)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(t,r=!0){return Nd?.findTestabilityInTree(this,t,r)??null}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function rS(e){Nd=e}var Nd,oS=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>new Au})}return e})(),Au=class{queuedEffectCount=0;queues=new Map;schedule(n){this.enqueue(n)}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),this.queuedEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||(this.queuedEffectCount++,r.add(n))}flush(){for(;this.queuedEffectCount>0;)for(let[n,t]of this.queues)n===null?this.flushQueue(t):n.run(()=>this.flushQueue(t))}flushQueue(n){for(let t of n)n.delete(t),this.queuedEffectCount--,t.run()}};function kn(e){return!!e&&typeof e.then=="function"}function $y(e){return!!e&&typeof e.subscribe=="function"}var ga=new A("");function Od(e){return Ys([{provide:ga,multi:!0,useValue:e}])}var zy=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=v(ga,{optional:!0})??[];injector=v(J);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=Fe(this.injector,o);if(kn(i))t.push(i);else if($y(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kd=new A("");function iS(){wl(()=>{throw new x(600,!1)})}function sS(e){return e.isBoundToModule}var aS=10;function Gy(e,n){return Array.isArray(n)?n.reduce(Gy,e):I(I({},e),n)}var Dt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=v(Bw);afterRenderManager=v($v);zonelessEnabled=v(Mv);rootEffectScheduler=v(oS);dirtyFlags=0;tracingSnapshot=null;externalTestViews=new Set;afterTick=new X;get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];isStable=v(jt).hasPendingTasks.pipe(U(t=>!t));constructor(){v(Ar,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=v(ie);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=J.NULL){re(10);let i=t instanceof Ty;if(!this._injector.get(zy).done){let f="";throw new x(405,f)}let a;i?a=t:a=this._injector.get(fa).resolveComponentFactory(t),this.componentTypes.push(a.componentType);let c=sS(a)?void 0:this._injector.get(br),l=r||a.selector,d=a.create(o,[],l,c),h=d.location.nativeElement,p=d.injector.get(Rd,null);return p?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),Ds(this.components,d),p?.unregisterApplication(h)}),this._loadComponent(d),re(11,d),d}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){re(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(cd.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new x(101,!1);let t=q(null);try{this._runningTick=!0,this.synchronize()}catch(r){this.internalErrorHandler(r)}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,q(t),this.afterTick.next(),re(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Cr,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<aS;)re(14),this.synchronizeOnce(),re(15)}synchronizeOnce(){if(this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush()),this.dirtyFlags&7){let t=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:r,notifyErrorHandler:o}of this.allViews)cS(r,o,t,this.zonelessEnabled);if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}else this._rendererFactory?.begin?.(),this._rendererFactory?.end?.();this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>ea(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;Ds(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(kd,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>Ds(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new x(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ds(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function cS(e,n,t,r){if(!t&&!ea(e))return;Cy(e,n,t&&!r?0:1)}function ot(e,n,t,r){let o=Z(),i=ta();if(An(o,i,n)){let s=fe(),a=na();a_(a,o,e,n,t,r)}return ot}function Wy(e,n,t,r){return An(e,ta(),t)?n+bn(t)+r:bt}function lS(e,n,t,r,o,i){let s=Xb(),a=JM(e,s,t,o);return ov(2),a?n+bn(t)+r+bn(o)+i:bt}function fs(e,n){return e<<17|n<<2}function Rn(e){return e>>17&32767}function uS(e){return(e&2)==2}function dS(e,n){return e&131071|n<<17}function Ru(e){return e|2}function wr(e){return(e&131068)>>2}function jl(e,n){return e&-131069|n<<2}function fS(e){return(e&1)===1}function Nu(e){return e|1}function hS(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Rn(s),c=wr(s);e[r]=t;let l=!1,d;if(Array.isArray(t)){let h=t;d=h[1],(d===null||bo(h,d)>0)&&(l=!0)}else d=t;if(o)if(c!==0){let p=Rn(e[a+1]);e[r+1]=fs(p,a),p!==0&&(e[p+1]=jl(e[p+1],r)),e[a+1]=dS(e[a+1],r)}else e[r+1]=fs(a,0),a!==0&&(e[a+1]=jl(e[a+1],r)),a=r;else e[r+1]=fs(c,0),a===0?a=r:e[c+1]=jl(e[c+1],r),c=r;l&&(e[r+1]=Ru(e[r+1])),dm(e,d,r,!0),dm(e,d,r,!1),pS(n,d,e,r,i),s=fs(a,c),i?n.classBindings=s:n.styleBindings=s}function pS(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&bo(i,n)>=0&&(t[r+1]=Nu(t[r+1]))}function dm(e,n,t,r){let o=e[t+1],i=n===null,s=r?Rn(o):wr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];gS(c,n)&&(a=!0,e[s+1]=r?Nu(l):Ru(l)),s=r?Rn(l):wr(l)}a&&(e[t+1]=r?Ru(o):Nu(o))}function gS(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?bo(e,n)>=0:!1}function tn(e,n,t){let r=Z(),o=ta();if(An(r,o,n)){let i=fe(),s=na();yd(i,s,r,e,n,r[ue],t,!1)}return tn}function fm(e,n,t,r,o){Id(n,e,t,o?"class":"style",r)}function ma(e,n){return mS(e,n,null,!0),ma}function mS(e,n,t,r){let o=Z(),i=fe(),s=ov(2);if(i.firstUpdatePass&&yS(i,e,s,r),n!==bt&&An(o,s,n)){let a=i.data[On()];wS(i,a,o,o[ue],e,o[s+1]=ES(n,t),r,s)}}function vS(e,n){return n>=e.expandoStartIndex}function yS(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[On()],s=vS(e,t);_S(i,r)&&n===null&&!s&&(n=!1),n=DS(o,i,n,r),hS(o,i,n,t,s,r)}}function DS(e,n,t,r){let o=rw(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Vl(null,e,n,t,r),t=Io(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Vl(o,e,n,t,r),i===null){let c=IS(e,n,r);c!==void 0&&Array.isArray(c)&&(c=Vl(null,e,n,c[1],r),c=Io(c,n.attrs,r),CS(e,n,r,c))}else i=bS(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function IS(e,n,t){let r=t?n.classBindings:n.styleBindings;if(wr(r)!==0)return e[Rn(r)]}function CS(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Rn(o)]=r}function bS(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Io(r,s,t)}return Io(r,n.attrs,t)}function Vl(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=Io(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Io(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),pb(e,s,t?!0:n[++i]))}return e===void 0?null:e}function wS(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,l=c[a+1],d=fS(l)?hm(c,n,t,o,wr(l),s):void 0;if(!$s(d)){$s(i)||uS(l)&&(i=hm(c,null,t,o,a,s));let h=Qm(On(),t);__(r,s,h,o,i)}}function hm(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),d=l?c[1]:c,h=d===null,p=t[o+1];p===bt&&(p=h?Ze:void 0);let f=h?Tl(p,r):d===r?p:void 0;if(l&&!$s(f)&&(f=Tl(c,r)),$s(f)&&(a=f,s))return a;let y=e[o+1];o=s?Rn(y):wr(y)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Tl(c,r))}return a}function $s(e){return e!==void 0}function ES(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=Ne(To(e)))),e}function _S(e,n){return(e.flags&(n?8:16))!==0}function Rr(e,n,t,r){let o=Z(),i=fe(),s=Ke+e,a=o[ue],c=i.firstCreatePass?Ry(s,i,o,n,Dd,qu(),t,r):i.data[s],l=MS(i,o,c,a,n,e);o[s]=l;let d=Js(c);return Nn(c,!0),iy(a,l,c),!wd(c)&&ra()&&ua(i,o,l,c),(Gb()===0||d)&&xr(l,o),Wb(),d&&(la(i,o,c),ud(i,c,o)),r!==null&&vd(o,c),Rr}function Nr(){let e=Se();Qu()?Ku():(e=e.parent,Nn(e,!1));let n=e;Zb(n)&&Yb(),qb();let t=fe();return t.firstCreatePass&&Ny(t,n),n.classesWithoutHost!=null&&lw(n)&&fm(t,n,Z(),n.classesWithoutHost,!0),n.stylesWithoutHost!=null&&uw(n)&&fm(t,n,Z(),n.stylesWithoutHost,!1),Nr}function Fd(e,n,t,r){return Rr(e,n,t,r),Nr(),Fd}var MS=(e,n,t,r,o,i)=>(oa(!0),ry(r,o,sw()));function SS(e,n,t,r,o){let i=n.consts,s=yr(i,r),a=Ao(n,e,8,"ng-container",s);s!==null&&bu(a,s,!0);let c=yr(i,o);return qu()&&Td(n,t,a,c,Dd),a.mergedAttrs=Dr(a.mergedAttrs,a.attrs),n.queries!==null&&n.queries.elementStart(n,a),a}function va(e,n,t){let r=Z(),o=fe(),i=e+Ke,s=o.firstCreatePass?SS(i,o,r,n,t):o.data[i];Nn(s,!0);let a=TS(o,r,s,e);return r[i]=a,ra()&&ua(o,r,a,s),xr(a,r),Js(s)&&(la(o,r,s),ud(o,s,r)),t!=null&&vd(r,s),va}function ya(){let e=Se(),n=fe();return Qu()?Ku():(e=e.parent,Nn(e,!1)),n.firstCreatePass&&(td(n,e),$u(e)&&n.queries.elementEnd(e)),ya}function Da(e,n,t){return va(e,n,t),ya(),Da}var TS=(e,n,t,r)=>(oa(!0),WE(n[ue],""));function qy(){return Z()}var zs="en-US";var xS=zs;function AS(e){typeof e=="string"&&(xS=e.toLowerCase().replace(/_/g,"-"))}function pm(e,n,t){return function r(o){if(o===Function)return t;let i=Sr(e)?gt(e.index,n):n;Md(i,5);let s=n[ke],a=gm(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=gm(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function gm(e,n,t,r){let o=q(null);try{return re(6,n,t),t(r)!==!1}catch(i){return RS(e,i),!1}finally{re(7,n,t),q(o)}}function RS(e,n){let t=e[mr],r=t?t.get(mt,null):null;r&&r.handleError(n)}function mm(e,n,t,r,o,i){let s=n[t],a=n[G],l=a.data[t].outputs[r],d=s[l],h=a.firstCreatePass?Wu(a):null,p=Gu(n),f=d.subscribe(i),y=p.length;p.push(i,f),h&&h.push(o,e.index,y,-(y+1))}var NS=(e,n,t)=>{};function ye(e,n,t,r){let o=Z(),i=fe(),s=Se();return Zy(i,o,o[ue],s,e,n,r),ye}function OS(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[_s],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Zy(e,n,t,r,o,i,s){let a=Js(r),l=e.firstCreatePass?Wu(e):null,d=Gu(n),h=!0;if(r.type&3||s){let p=It(r,n),f=s?s(p):p,y=d.length,S=s?z=>s(pt(z[r.index])):r.index,N=null;if(!s&&a&&(N=OS(e,n,o,r.index)),N!==null){let z=N.__ngLastListenerFn__||N;z.__ngNextListenerFn__=i,N.__ngLastListenerFn__=i,h=!1}else{i=pm(r,n,i),NS(f,o,i);let z=t.listen(f,o,i);d.push(i,z),l&&l.push(o,S,y,y+1)}}else i=pm(r,n,i);if(h){let p=r.outputs?.[o],f=r.hostDirectiveOutputs?.[o];if(f&&f.length)for(let y=0;y<f.length;y+=2){let S=f[y],N=f[y+1];mm(r,n,S,N,o,i)}if(p&&p.length)for(let y of p)mm(r,n,y,o,o,i)}}function ko(e=1){return iw(e)}function kS(e,n){let t=null,r=LE(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?ny(e,i,!0):BE(r,i))return o}return t}function E(e){let n=Z()[Qe][Pe];if(!n.projection){let t=e?e.length:1,r=n.projection=ps(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?kS(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function b(e,n=0,t,r,o,i){let s=Z(),a=fe(),c=r?e+1:null;c!==null&&Uy(s,a,c,r,o,i,null,t);let l=Ao(a,Ke+e,16,null,t||null);l.projection===null&&(l.projection=n),Ku();let h=!s[mo]||tv();s[Qe][Pe].projection[l.projection]===null&&c!==null?FS(s,a,c):h&&!wd(l)&&w_(a,s,l)}function FS(e,n,t){let r=Ke+t,o=n.data[r],i=e[r],s=Du(i,o.tView.ssrId),a=uy(e,o,void 0,{dehydratedView:s});My(i,a,0,gu(o,s))}function PS(e,n,t){return Yy(e,"",n,"",t),PS}function Yy(e,n,t,r,o){let i=Z(),s=Wy(i,n,t,r);if(s!==bt){let a=fe(),c=na();yd(a,c,i,e,s,i[ue],o,!1)}return Yy}function nn(e,n,t,r){MM(e,n,t,r)}function Fo(e,n,t){_M(e,n,t)}function it(e){let n=Z(),t=fe(),r=iv();Xu(r+1);let o=xd(t,r);if(e.dirty&&Bb(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=xM(n,r);e.reset(i,$w),e.notifyOnChanges()}return!0}return!1}function st(){return EM(Z(),iv())}function s2(e,n=""){let t=Z(),r=fe(),o=e+Ke,i=r.firstCreatePass?Ao(r,o,1,n,null):r.data[o],s=LS(r,t,i,n,e);t[o]=s,ra()&&ua(r,t,s,i),Nn(i,!1)}var LS=(e,n,t,r,o)=>(oa(!0),zE(n[ue],r));function jS(e){return Qy("",e,""),jS}function Qy(e,n,t){let r=Z(),o=Wy(r,e,n,t);return o!==bt&&Ky(r,On(),o),Qy}function VS(e,n,t,r,o){let i=Z(),s=lS(i,e,n,t,r,o);return s!==bt&&Ky(i,On(),s),VS}function Ky(e,n,t){let r=Qm(n,e);GE(e[ue],r,t)}function BS(e,n,t){Nv(n)&&(n=n());let r=Z(),o=ta();if(An(r,o,n)){let i=fe(),s=na();yd(i,s,r,e,n,r[ue],t,!1)}return BS}function a2(e,n){let t=Nv(e);return t&&e.set(n),t}function US(e,n){let t=Z(),r=fe(),o=Se();return Zy(r,t,t[ue],o,e,n),US}function HS(e,n,t){let r=fe();if(r.firstCreatePass){let o=ht(e);Ou(t,r.data,r.blueprint,o,!0),Ou(n,r.data,r.blueprint,o,!1)}}function Ou(e,n,t,r,o){if(e=Ee(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ou(e[i],n,t,r,o);else{let i=fe(),s=Z(),a=Se(),c=gr(e)?e:Ee(e.provide),l=Hm(e),d=a.providerIndexes&1048575,h=a.directiveStart,p=a.providerIndexes>>20;if(gr(e)||!e.multi){let f=new Tn(l,o,u),y=Ul(c,n,o?d:d+p,h);y===-1?(tu(Rs(a,s),i,c),Bl(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[y]=f,s[y]=f)}else{let f=Ul(c,n,d+p,h),y=Ul(c,n,d,d+p),S=f>=0&&t[f],N=y>=0&&t[y];if(o&&!N||!o&&!S){tu(Rs(a,s),i,c);let z=GS(o?zS:$S,t.length,o,r,l);!o&&N&&(t[y].providerFactory=z),Bl(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(z),s.push(z)}else{let z=Xy(t[o?y:f],l,!o&&r);Bl(i,e,f>-1?f:y,z)}!o&&r&&N&&t[y].componentProviders++}}}function Bl(e,n,t,r){let o=gr(n),i=bb(n);if(o||i){let c=(i?Ee(n.useClass):n).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let d=l.indexOf(t);d===-1?l.push(t,[r,c]):l[d+1].push(r,c)}else l.push(t,c)}}}function Xy(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Ul(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function $S(e,n,t,r){return ku(this.multi,[])}function zS(e,n,t,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=yo(t,t[G],this.providerFactory.index,r);i=a.slice(0,s),ku(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],ku(o,i);return i}function ku(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function GS(e,n,t,r,o){let i=new Tn(e,t,u);return i.multi=[],i.index=n,i.componentProviders=0,Xy(i,o,r&&!t),i}function Ae(e,n=[]){return t=>{t.providersResolver=(r,o)=>HS(r,o?o(e):e,n)}}function c2(e,n,t){let r=Kb()+e,o=Z();return o[r]===bt?KM(o,r,t?n.call(t):n()):XM(o,r)}var hs=null;function WS(e){hs!==null&&(e.defaultEncapsulation!==hs.defaultEncapsulation||e.preserveWhitespaces!==hs.preserveWhitespaces)||(hs=e)}var Fu=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},Ia=(()=>{class e{compileModuleSync(t){return new Hs(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=jm(t),i=ey(o.declarations).reduce((s,a)=>{let c=Xt(a);return c&&s.push(new xn(c)),s},[]);return new Fu(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),qS=new A("");function ZS(e,n,t){let r=new Hs(t);return Promise.resolve(r)}function vm(e){for(let n=e.length-1;n>=0;n--)if(e[n]!==void 0)return e[n]}var YS=(()=>{class e{zone=v(g);changeDetectionScheduler=v(Ir);applicationRef=v(Dt);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function QS({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new g(P(I({},Jy()),{scheduleInRootZone:t})),[{provide:g,useFactory:e},{provide:po,multi:!0,useFactory:()=>{let r=v(YS,{optional:!0});return()=>r.initialize()}},{provide:po,multi:!0,useFactory:()=>{let r=v(KS);return()=>{r.initialize()}}},n===!0?{provide:Sv,useValue:!0}:[],{provide:Tv,useValue:t??_v}]}function Jy(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var KS=(()=>{class e{subscription=new le;initialized=!1;zone=v(g);pendingTasks=v(jt);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{g.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{g.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var XS=(()=>{class e{appRef=v(Dt);taskService=v(jt);ngZone=v(g);zonelessEnabled=v(Mv);tracing=v(Ar,{optional:!0});disableScheduling=v(Sv,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new le;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Os):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(v(Tv,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof ks||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Gg:xv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Os+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){throw this.taskService.remove(t),r}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Gg(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function JS(){return typeof $localize<"u"&&$localize.locale||zs}var Pd=new A("",{providedIn:"root",factory:()=>v(Pd,H.Optional|H.SkipSelf)||JS()});var Gs=new A(""),eT=new A("");function ao(e){return!e.moduleRef}function tT(e){let n=ao(e)?e.r3Injector:e.moduleRef.injector,t=n.get(g);return t.run(()=>{ao(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(mt,null),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:i=>{r.handleError(i)}})}),ao(e)){let i=()=>n.destroy(),s=e.platformInjector.get(Gs);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Gs);s.add(i),e.moduleRef.onDestroy(()=>{Ds(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return rT(r,t,()=>{let i=n.get(zy);return i.runInitializers(),i.donePromise.then(()=>{let s=n.get(Pd,zs);if(AS(s||zs),!n.get(eT,!0))return ao(e)?n.get(Dt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(ao(e)){let c=n.get(Dt);return e.rootComponent!==void 0&&c.bootstrap(e.rootComponent),c}else return nT(e.moduleRef,e.allPlatformModules),e.moduleRef})})})}function nT(e,n){let t=e.injector.get(Dt);if(e._bootstrapComponents.length>0)e._bootstrapComponents.forEach(r=>t.bootstrap(r));else if(e.instance.ngDoBootstrap)e.instance.ngDoBootstrap(t);else throw new x(-403,!1);n.push(e)}function rT(e,n,t){try{let r=t();return kn(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e.handleError(r)),r}}var eD=(()=>{class e{_injector;_modules=[];_destroyListeners=[];_destroyed=!1;constructor(t){this._injector=t}bootstrapModuleFactory(t,r){let o=r?.scheduleInRootZone,i=()=>Vw(r?.ngZone,P(I({},Jy({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing})),{scheduleInRootZone:o})),s=r?.ignoreChangesOutsideZone,a=[QS({ngZoneFactory:i,ignoreChangesOutsideZone:s}),{provide:Ir,useExisting:XS}],c=PM(t.moduleType,this.injector,a);return tT({moduleRef:c,allPlatformModules:this._modules,platformInjector:this.injector})}bootstrapModule(t,r=[]){let o=Gy({},r);return ZS(this.injector,o,t).then(i=>this.bootstrapModuleFactory(i,o))}onDestroy(t){this._destroyListeners.push(t)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new x(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let t=this._injector.get(Gs,null);t&&(t.forEach(r=>r()),t.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static \u0275fac=function(r){return new(r||e)(R(J))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),ho=null,tD=new A("");function oT(e){if(ho&&!ho.get(tD,!1))throw new x(400,!1);iS(),ho=e;let n=e.get(eD);return aT(e),n}function Ld(e,n,t=[]){let r=`Platform: ${n}`,o=new A(r);return(i=[])=>{let s=nD();if(!s||s.injector.get(tD,!1)){let a=[...t,...i,{provide:o,useValue:!0}];e?e(a):oT(iT(a,r))}return sT(o)}}function iT(e=[],n){return J.create({name:n,providers:[{provide:Qs,useValue:"platform"},{provide:Gs,useValue:new Set([()=>ho=null])},...e]})}function sT(e){let n=nD();if(!n)throw new x(401,!1);return n}function nD(){return ho?.get(eD)??null}function aT(e){let n=e.get(sd,null);Fe(e,()=>{n?.forEach(t=>t())})}var D=(()=>{class e{static __NG_ELEMENT_ID__=cT}return e})();function cT(e){return lT(Se(),Z(),(e&16)===16)}function lT(e,n,t){if(Sr(e)&&!t){let r=gt(e.index,n);return new Do(r,r)}else if(e.type&175){let r=n[Qe];return new Do(r,n)}return null}var Pu=class{constructor(){}supports(n){return By(n)}create(n){return new Lu(n)}},uT=(e,n)=>n,Lu=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||uT}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<ym(r,o,i)?t:r,a=ym(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,d=c-o;if(l!=d){for(let p=0;p<l;p++){let f=p<i.length?i[p]:i[p]=0,y=f+p;d<=y&&y<l&&(i[p]=f+1)}let h=s.previousIndex;i[h]=d-l}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!By(n))throw new x(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,YM(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new ju(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Ws),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Ws),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},ju=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},Vu=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Ws=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Vu,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function ym(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function Dm(){return new jd([new Pu])}var jd=(()=>{class e{factories;static \u0275prov=T({token:e,providedIn:"root",factory:Dm});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||Dm()),deps:[[e,new km,new Om]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new x(901,!1)}}return e})();var rD=Ld(null,"core",[]),oD=(()=>{class e{constructor(t){}static \u0275fac=function(r){return new(r||e)(R(Dt))};static \u0275mod=xe({type:e});static \u0275inj=Me({})}return e})();function rn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Bt(e){return Ml(e)}function Po(e,n){return bl(e,n?.equal)}var Im=class{[We];constructor(n){this[We]=n}destroy(){this[We].destroy()}};function iD(e,n){let t=Xt(e),r=n.elementInjector||Ks();return new xn(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector)}function Ca(e){let n=Xt(e);if(!n)return null;let t=new xn(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var se=new A("");var cD=null;function He(){return cD}function Vd(e){cD??=e}var Lo=class{},jo=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(lD),providedIn:"platform"})}return e})(),Bd=new A(""),lD=(()=>{class e extends jo{_location;_history;_doc=v(se);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return He().getBaseHref(this._doc)}onPopState(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=He().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function ba(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function sD(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function ct(e){return e&&e[0]!=="?"?`?${e}`:e}var Le=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(Ea),providedIn:"root"})}return e})(),wa=new A(""),Ea=(()=>{class e extends Le{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??v(se).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return ba(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+ct(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(R(jo),R(wa,8))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$e=(()=>{class e{_subject=new X;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=hT(sD(aD(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+ct(r))}normalize(t){return e.stripTrailingSlash(fT(this._basePath,aD(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+ct(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+ct(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=ct;static joinWithSlash=ba;static stripTrailingSlash=sD;static \u0275fac=function(r){return new(r||e)(R(Le))};static \u0275prov=T({token:e,factory:()=>dT(),providedIn:"root"})}return e})();function dT(){return new $e(R(Le))}function fT(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function aD(e){return e.replace(/\/index.html$/,"")}function hT(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var Hd=(()=>{class e extends Le{_platformLocation;_baseHref="";_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}path(t=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(t){let r=ba(this._baseHref,t);return r.length>0?"#"+r:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i))||this._platformLocation.pathname;this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+ct(i))||this._platformLocation.pathname;this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(R(jo),R(wa,8))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var Ud=/\s+/,uD=[],pT=(()=>{class e{_ngEl;_renderer;initialClasses=uD;rawClass;stateMap=new Map;constructor(t,r){this._ngEl=t,this._renderer=r}set klass(t){this.initialClasses=t!=null?t.trim().split(Ud):uD}set ngClass(t){this.rawClass=typeof t=="string"?t.trim().split(Ud):t}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let t=this.rawClass;if(Array.isArray(t)||t instanceof Set)for(let r of t)this._updateState(r,!0);else if(t!=null)for(let r of Object.keys(t))this._updateState(r,!!t[r]);this._applyStateDiff()}_updateState(t,r){let o=this.stateMap.get(t);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(t,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let t of this.stateMap){let r=t[0],o=t[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(t,r){t=t.trim(),t.length>0&&t.split(Ud).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static \u0275fac=function(r){return new(r||e)(u(m),u(en))};static \u0275dir=V({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"}})}return e})();var _a=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},hD=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new _a(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),dD(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);dD(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(u(Ue),u(yt),u(jd))};static \u0275dir=V({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function dD(e,n){e.context.$implicit=n.item}var Vo=(()=>{class e{_viewContainer;_context=new Ma;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){fD(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){fD(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(u(Ue),u(yt))};static \u0275dir=V({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ma=class{$implicit=null;ngIf=null};function fD(e,n){if(e&&!e.createEmbeddedView)throw new x(2020,!1)}var Sa=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(u(Ue))};static \u0275dir=V({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Xe]})}return e})();var Bo=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({})}return e})();function Uo(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var $d="browser",pD="server";function Ta(e){return e===pD}var Fn=class{};var gD=(()=>{class e{static \u0275prov=T({token:e,providedIn:"root",factory:()=>new zd(v(se),window)})}return e})(),zd=class{document;window;offset=()=>[0,0];constructor(n,t){this.document=n,this.window=t}setOffset(n){Array.isArray(n)?this.offset=()=>n:this.offset=n}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(n){this.window.scrollTo(n[0],n[1])}scrollToAnchor(n){let t=mT(this.document,n);t&&(this.scrollToElement(t),t.focus())}setHistoryScrollRestoration(n){this.window.history.scrollRestoration=n}scrollToElement(n){let t=n.getBoundingClientRect(),r=t.left+this.window.pageXOffset,o=t.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function mT(e,n){let t=e.getElementById(n)||e.getElementsByName(n)[0];if(t)return t;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(n)||i.querySelector(`[name="${n}"]`);if(s)return s}o=r.nextNode()}}return null}var Ra=new A(""),Yd=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new x(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(R(Ra),R(g))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Ho=class{_doc;constructor(n){this._doc=n}manager},xa="ng-app-id";function mD(e){for(let n of e)n.remove()}function vD(e,n){let t=n.createElement("style");return t.textContent=e,t}function vT(e,n,t,r){let o=e.head?.querySelectorAll(`style[${xa}="${n}"],link[${xa}="${n}"]`);if(o)for(let i of o)i.removeAttribute(xa),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function qd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var Qd=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;isServer;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,this.isServer=Ta(i),vT(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,vD);r?.forEach(o=>this.addUsage(o,this.external,qd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(mD(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])mD(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,vD(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,qd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),this.isServer&&r.setAttribute(xa,this.appId),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(R(se),R(id),R(ad,8),R(Jt))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Wd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Kd=/%COMP%/g;var DD="%COMP%",yT=`_nghost-${DD}`,DT=`_ngcontent-${DD}`,IT=!0,CT=new A("",{providedIn:"root",factory:()=>IT});function bT(e){return DT.replace(Kd,e)}function wT(e){return yT.replace(Kd,e)}function ID(e,n){return n.map(t=>t.replace(Kd,e))}var Xd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,l=null,d=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.tracingService=d,this.platformIsServer=Ta(a),this.defaultRenderer=new $o(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===vt.ShadowDom&&(r=P(I({},r),{encapsulation:vt.Emulated}));let o=this.getOrCreateRenderer(t,r);return o instanceof Aa?o.applyToHost(t):o instanceof zo&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,d=this.removeStylesOnCompDestroy,h=this.platformIsServer,p=this.tracingService;switch(r.encapsulation){case vt.Emulated:i=new Aa(c,l,r,this.appId,d,s,a,h,p);break;case vt.ShadowDom:return new Zd(c,l,t,r,s,a,this.nonce,h,p);default:i=new zo(c,l,r,d,s,a,h,p);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(R(Yd),R(Qd),R(id),R(CT),R(se),R(Jt),R(g),R(ad),R(Ar,8))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),$o=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Wd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(yD(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(yD(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new x(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=Wd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=Wd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(Ft.DashCase|Ft.Important)?n.style.setProperty(t,r,o&Ft.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&Ft.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=He().getGlobalEventTarget(this.doc,n),!n))throw new x(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;(this.platformIsServer?this.ngZone.runGuarded(()=>n(t)):n(t))===!1&&t.preventDefault()}}};function yD(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Zd=class extends $o{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,l){super(n,i,s,c,l),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let d=o.styles;d=ID(o.id,d);for(let p of d){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=p,this.shadowRoot.appendChild(f)}let h=o.getExternalStyles?.();if(h)for(let p of h){let f=qd(p,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},zo=class extends $o{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,l){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let d=r.styles;this.styles=l?ID(l,d):d,this.styleUrls=r.getExternalStyles?.(l)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Aa=class extends zo{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,l){let d=o+"-"+r.id;super(n,t,r,i,s,a,c,l,d),this.contentAttr=bT(d),this.hostAttr=wT(d)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var Na=class e extends Lo{supportsDOMEvents=!0;static makeCurrent(){Vd(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=ET();return t==null?null:_T(t)}resetBaseElement(){Go=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Uo(document.cookie,n)}},Go=null;function ET(){return Go=Go||document.querySelector("base"),Go?Go.getAttribute("href"):null}function _T(e){return new URL(e,document.baseURI).pathname}var Oa=class{addToWindow(n){Oe.getAngularTestability=(r,o=!0)=>{let i=n.findTestabilityInTree(r,o);if(i==null)throw new x(5103,!1);return i},Oe.getAllAngularTestabilities=()=>n.getAllTestabilities(),Oe.getAllAngularRootElements=()=>n.getAllRootElements();let t=r=>{let o=Oe.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};Oe.frameworkStabilizers||(Oe.frameworkStabilizers=[]),Oe.frameworkStabilizers.push(t)}findTestabilityInTree(n,t,r){if(t==null)return null;let o=n.getTestability(t);return o??(r?He().isShadowRoot(t)?this.findTestabilityInTree(n,t.host,!0):this.findTestabilityInTree(n,t.parentElement,!0):null)}},MT=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),bD=(()=>{class e extends Ho{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),CD=["alt","control","meta","shift"],ST={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},TT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},wD=(()=>{class e extends Ho{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>He().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),CD.forEach(l=>{let d=r.indexOf(l);d>-1&&(r.splice(d,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=ST[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),CD.forEach(s=>{if(s!==o){let a=TT[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function xT(){Na.makeCurrent()}function AT(){return new mt}function RT(){return Vv(document),document}var NT=[{provide:Jt,useValue:$d},{provide:sd,useValue:xT,multi:!0},{provide:se,useFactory:RT}],OT=Ld(rD,"browser",NT);var kT=[{provide:Oo,useClass:Oa},{provide:Rd,useClass:ha,deps:[g,pa,Oo]},{provide:ha,useClass:ha,deps:[g,pa,Oo]}],FT=[{provide:Qs,useValue:"root"},{provide:mt,useFactory:AT},{provide:Ra,useClass:bD,multi:!0,deps:[se]},{provide:Ra,useClass:wD,multi:!0,deps:[se]},Xd,Qd,Yd,{provide:Cr,useExisting:Xd},{provide:Fn,useClass:MT},[]],PT=(()=>{class e{constructor(){}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({providers:[...FT,...kT],imports:[Bo,oD]})}return e})();var kr=class{},Wo=class{},on=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Fa=class{encodeKey(n){return ED(n)}encodeValue(n){return ED(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function LT(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var jT=/%(\d[a-f0-9])/gi,VT={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function ED(e){return encodeURIComponent(e).replace(jT,(n,t)=>VT[t]??n)}function ka(e){return`${e}`}var Ut=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new Fa,n.fromString){if(n.fromObject)throw new x(2805,!1);this.map=LT(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(ka):[ka(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(ka(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(ka(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Pa=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function BT(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function _D(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function MD(e){return typeof Blob<"u"&&e instanceof Blob}function SD(e){return typeof FormData<"u"&&e instanceof FormData}function UT(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var TD="Content-Type",xD="Accept",RD="X-Request-URL",ND="text/plain",OD="application/json",HT=`${OD}, ${ND}, */*`,Or=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;responseType="json";method;params;urlWithParams;transferCache;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(BT(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new on,this.context??=new Pa,!this.params)this.params=new Ut,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||_D(this.body)||MD(this.body)||SD(this.body)||UT(this.body)?this.body:this.body instanceof Ut?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||SD(this.body)?null:MD(this.body)?this.body.type||null:_D(this.body)?null:typeof this.body=="string"?ND:this.body instanceof Ut?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?OD:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.transferCache??this.transferCache,s=n.body!==void 0?n.body:this.body,a=n.withCredentials??this.withCredentials,c=n.reportProgress??this.reportProgress,l=n.headers||this.headers,d=n.params||this.params,h=n.context??this.context;return n.setHeaders!==void 0&&(l=Object.keys(n.setHeaders).reduce((p,f)=>p.set(f,n.setHeaders[f]),l)),n.setParams&&(d=Object.keys(n.setParams).reduce((p,f)=>p.set(f,n.setParams[f]),d)),new e(t,r,s,{params:d,headers:l,context:h,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Pn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Pn||{}),Fr=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new on,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},La=class e extends Fr{constructor(n={}){super(n)}type=Pn.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},qo=class e extends Fr{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=Pn.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Zo=class extends Fr{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},$T=200,zT=204;function Jd(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var kD=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof Or)i=t;else{let c;o.headers instanceof on?c=o.headers:c=new on(o.headers);let l;o.params&&(o.params instanceof Ut?l=o.params:l=new Ut({fromObject:o.params})),i=new Or(t,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=O(i).pipe(dt(c=>this.handler.handle(c)));if(t instanceof Or||o.observe==="events")return s;let a=s.pipe(me(c=>c instanceof qo));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(U(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new x(2806,!1);return c.body}));case"blob":return a.pipe(U(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new x(2807,!1);return c.body}));case"text":return a.pipe(U(c=>{if(c.body!==null&&typeof c.body!="string")throw new x(2808,!1);return c.body}));case"json":default:return a.pipe(U(c=>c.body))}case"response":return a;default:throw new x(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new Ut().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,Jd(o,r))}post(t,r,o={}){return this.request("POST",t,Jd(o,r))}put(t,r,o={}){return this.request("PUT",t,Jd(o,r))}static \u0275fac=function(r){return new(r||e)(R(kr))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var GT=new A("");function FD(e,n){return n(e)}function WT(e,n){return(t,r)=>n.intercept(t,{handle:o=>e(o,r)})}function qT(e,n,t){return(r,o)=>Fe(t,()=>n(r,i=>e(i,o)))}var PD=new A(""),tf=new A(""),LD=new A(""),nf=new A("",{providedIn:"root",factory:()=>!0});function ZT(){let e=null;return(n,t)=>{e===null&&(e=(v(PD,{optional:!0})??[]).reduceRight(WT,FD));let r=v(jt);if(v(nf)){let i=r.add();return e(n,t).pipe(Yt(()=>r.remove(i)))}else return e(n,t)}}var ja=(()=>{class e extends kr{backend;injector;chain=null;pendingTasks=v(jt);contributeToStability=v(nf);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(tf),...this.injector.get(LD,[])]));this.chain=r.reduceRight((o,i)=>qT(o,i,this.injector),FD)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(Yt(()=>this.pendingTasks.remove(r)))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(R(Wo),R(ie))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var YT=/^\)\]\}',?\n/,QT=RegExp(`^${RD}:`,"m");function KT(e){return"responseURL"in e&&e.responseURL?e.responseURL:QT.test(e.getAllResponseHeaders())?e.getResponseHeader(RD):null}var ef=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new x(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?ne(r.\u0275loadImpl()):O(null)).pipe(ve(()=>new W(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((S,N)=>s.setRequestHeader(S,N.join(","))),t.headers.has(xD)||s.setRequestHeader(xD,HT),!t.headers.has(TD)){let S=t.detectContentTypeHeader();S!==null&&s.setRequestHeader(TD,S)}if(t.responseType){let S=t.responseType.toLowerCase();s.responseType=S!=="json"?S:"text"}let a=t.serializeBody(),c=null,l=()=>{if(c!==null)return c;let S=s.statusText||"OK",N=new on(s.getAllResponseHeaders()),z=KT(s)||t.url;return c=new La({headers:N,status:s.status,statusText:S,url:z}),c},d=()=>{let{headers:S,status:N,statusText:z,url:xt}=l(),ce=null;N!==zT&&(ce=typeof s.response>"u"?s.responseText:s.response),N===0&&(N=ce?$T:0);let Gn=N>=200&&N<300;if(t.responseType==="json"&&typeof ce=="string"){let Mi=ce;ce=ce.replace(YT,"");try{ce=ce!==""?JSON.parse(ce):null}catch(XC){ce=Mi,Gn&&(Gn=!1,ce={error:XC,text:ce})}}Gn?(i.next(new qo({body:ce,headers:S,status:N,statusText:z,url:xt||void 0})),i.complete()):i.error(new Zo({error:ce,headers:S,status:N,statusText:z,url:xt||void 0}))},h=S=>{let{url:N}=l(),z=new Zo({error:S,status:s.status||0,statusText:s.statusText||"Unknown Error",url:N||void 0});i.error(z)},p=!1,f=S=>{p||(i.next(l()),p=!0);let N={type:Pn.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(N.total=S.total),t.responseType==="text"&&s.responseText&&(N.partialText=s.responseText),i.next(N)},y=S=>{let N={type:Pn.UploadProgress,loaded:S.loaded};S.lengthComputable&&(N.total=S.total),i.next(N)};return s.addEventListener("load",d),s.addEventListener("error",h),s.addEventListener("timeout",h),s.addEventListener("abort",h),t.reportProgress&&(s.addEventListener("progress",f),a!==null&&s.upload&&s.upload.addEventListener("progress",y)),s.send(a),i.next({type:Pn.Sent}),()=>{s.removeEventListener("error",h),s.removeEventListener("abort",h),s.removeEventListener("load",d),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",f),a!==null&&s.upload&&s.upload.removeEventListener("progress",y)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(R(Fn))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),jD=new A(""),XT="XSRF-TOKEN",JT=new A("",{providedIn:"root",factory:()=>XT}),ex="X-XSRF-TOKEN",tx=new A("",{providedIn:"root",factory:()=>ex}),Yo=class{},nx=(()=>{class e{doc;platform;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r,o){this.doc=t,this.platform=r,this.cookieName=o}getToken(){if(this.platform==="server")return null;let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Uo(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(R(se),R(Jt),R(JT))};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function rx(e,n){let t=e.url.toLowerCase();if(!v(jD)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=v(Yo).getToken(),o=v(tx);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var rf=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(rf||{});function ox(e,n){return{\u0275kind:e,\u0275providers:n}}function VD(...e){let n=[kD,ef,ja,{provide:kr,useExisting:ja},{provide:Wo,useFactory:()=>v(GT,{optional:!0})??v(ef)},{provide:tf,useValue:rx,multi:!0},{provide:jD,useValue:!0},{provide:Yo,useClass:nx}];for(let t of e)n.push(...t.\u0275providers);return Ys(n)}var AD=new A("");function BD(){return ox(rf.LegacyInterceptors,[{provide:AD,useFactory:ZT},{provide:tf,useExisting:AD,multi:!0}])}var ix=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({providers:[VD(BD())]})}return e})();var UD=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(R(se))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var F="primary",ci=Symbol("RouteTitle"),lf=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Vn(e){return new lf(e)}function YD(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function ax(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!wt(e[t],n[t]))return!1;return!0}function wt(e,n){let t=e?uf(e):void 0,r=n?uf(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!QD(e[o],n[o]))return!1;return!0}function uf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function QD(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function KD(e){return e.length>0?e[e.length-1]:null}function ln(e){return rl(e)?e:kn(e)?ne(Promise.resolve(e)):O(e)}var cx={exact:JD,subset:eI},XD={exact:lx,subset:ux,ignored:()=>!0};function HD(e,n,t){return cx[t.paths](e.root,n.root,t.matrixParams)&&XD[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function lx(e,n){return wt(e,n)}function JD(e,n,t){if(!Ln(e.segments,n.segments)||!Ua(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!JD(e.children[r],n.children[r],t))return!1;return!0}function ux(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>QD(e[t],n[t]))}function eI(e,n,t){return tI(e,n,n.segments,t)}function tI(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!Ln(o,t)||n.hasChildren()||!Ua(o,t,r))}else if(e.segments.length===t.length){if(!Ln(e.segments,t)||!Ua(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!eI(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!Ln(e.segments,o)||!Ua(e.segments,o,r)||!e.children[F]?!1:tI(e.children[F],n,i,r)}}function Ua(e,n,t){return n.every((r,o)=>XD[t](e[o].parameters,r.parameters))}var _t=class{root;queryParams;fragment;_queryParamMap;constructor(n=new Q([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Vn(this.queryParams),this._queryParamMap}toString(){return hx.serialize(this)}},Q=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ha(this)}},sn=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Vn(this.parameters),this._parameterMap}toString(){return rI(this)}};function dx(e,n){return Ln(e,n)&&e.every((t,r)=>wt(t.parameters,n[r].parameters))}function Ln(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function fx(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===F&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==F&&(t=t.concat(n(o,r)))}),t}var Ht=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>new an,providedIn:"root"})}return e})(),an=class{parse(n){let t=new ff(n);return new _t(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${Qo(n.root,!0)}`,r=mx(n.queryParams),o=typeof n.fragment=="string"?`#${px(n.fragment)}`:"";return`${t}${r}${o}`}},hx=new an;function Ha(e){return e.segments.map(n=>rI(n)).join("/")}function Qo(e,n){if(!e.hasChildren())return Ha(e);if(n){let t=e.children[F]?Qo(e.children[F],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==F&&r.push(`${o}:${Qo(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=fx(e,(r,o)=>o===F?[Qo(e.children[F],!1)]:[`${o}:${Qo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[F]!=null?`${Ha(e)}/${t[0]}`:`${Ha(e)}/(${t.join("//")})`}}function nI(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Va(e){return nI(e).replace(/%3B/gi,";")}function px(e){return encodeURI(e)}function df(e){return nI(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function $a(e){return decodeURIComponent(e)}function $D(e){return $a(e.replace(/\+/g,"%20"))}function rI(e){return`${df(e.path)}${gx(e.parameters)}`}function gx(e){return Object.entries(e).map(([n,t])=>`;${df(n)}=${df(t)}`).join("")}function mx(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${Va(t)}=${Va(o)}`).join("&"):`${Va(t)}=${Va(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var vx=/^[^\/()?;#]+/;function of(e){let n=e.match(vx);return n?n[0]:""}var yx=/^[^\/()?;=#]+/;function Dx(e){let n=e.match(yx);return n?n[0]:""}var Ix=/^[^=?&#]+/;function Cx(e){let n=e.match(Ix);return n?n[0]:""}var bx=/^[^&#]+/;function wx(e){let n=e.match(bx);return n?n[0]:""}var ff=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new Q([],{}):new Q([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[F]=new Q(n,t)),r}parseSegment(){let n=of(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new x(4009,!1);return this.capture(n),new sn($a(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=Dx(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=of(this.remaining);o&&(r=o,this.capture(r))}n[$a(t)]=$a(r)}parseQueryParam(n){let t=Cx(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=wx(this.remaining);s&&(r=s,this.capture(r))}let o=$D(t),i=$D(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=of(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new x(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=F);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[F]:new Q([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new x(4011,!1)}};function oI(e){return e.segments.length>0?new Q([],{[F]:e}):e}function iI(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=iI(o);if(r===F&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new Q(e.segments,n);return Ex(t)}function Ex(e){if(e.numberOfChildren===1&&e.children[F]){let n=e.children[F];return new Q(e.segments.concat(n.segments),n.children)}return e}function cn(e){return e instanceof _t}function sI(e,n,t=null,r=null){let o=aI(e);return cI(o,n,t,r)}function aI(e){let n;function t(i){let s={};for(let c of i.children){let l=t(c);s[c.outlet]=l}let a=new Q(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=oI(r);return n??o}function cI(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return sf(o,o,o,t,r);let i=_x(n);if(i.toRoot())return sf(o,o,new Q([],{}),t,r);let s=Mx(i,o,e),a=s.processChildren?Xo(s.segmentGroup,s.index,i.commands):uI(s.segmentGroup,s.index,i.commands);return sf(o,s.segmentGroup,a,t,r)}function Ga(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function ei(e){return typeof e=="object"&&e!=null&&e.outlets}function sf(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(d=>`${d}`):`${l}`});let s;e===n?s=t:s=lI(e,n,t);let a=oI(iI(s));return new _t(a,i,o)}function lI(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=lI(i,n,t)}),new Q(e.segments,r)}var Wa=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&Ga(r[0]))throw new x(4003,!1);let o=r.find(ei);if(o&&o!==KD(r))throw new x(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function _x(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Wa(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Wa(t,n,r)}var jr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function Mx(e,n,t){if(e.isAbsolute)return new jr(n,!0,0);if(!t)return new jr(n,!1,NaN);if(t.parent===null)return new jr(t,!0,0);let r=Ga(e.commands[0])?0:1,o=t.segments.length-1+r;return Sx(t,o,e.numberOfDoubleDots)}function Sx(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new x(4005,!1);o=r.segments.length}return new jr(r,!1,o-i)}function Tx(e){return ei(e[0])?e[0].outlets:{[F]:e}}function uI(e,n,t){if(e??=new Q([],{}),e.segments.length===0&&e.hasChildren())return Xo(e,n,t);let r=xx(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new Q(e.segments.slice(0,r.pathIndex),{});return i.children[F]=new Q(e.segments.slice(r.pathIndex),e.children),Xo(i,0,o)}else return r.match&&o.length===0?new Q(e.segments,{}):r.match&&!e.hasChildren()?hf(e,n,t):r.match?Xo(e,0,o):hf(e,n,t)}function Xo(e,n,t){if(t.length===0)return new Q(e.segments,{});{let r=Tx(t),o={};if(Object.keys(r).some(i=>i!==F)&&e.children[F]&&e.numberOfChildren===1&&e.children[F].segments.length===0){let i=Xo(e.children[F],n,t);return new Q(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=uI(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new Q(e.segments,o)}}function xx(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(ei(a))break;let c=`${a}`,l=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!GD(c,l,s))return i;r+=2}else{if(!GD(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function hf(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(ei(i)){let c=Ax(i.outlets);return new Q(r,c)}if(o===0&&Ga(t[0])){let c=e.segments[n];r.push(new sn(c.path,zD(t[0]))),o++;continue}let s=ei(i)?i.outlets[F]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&Ga(a)?(r.push(new sn(s,zD(a))),o+=2):(r.push(new sn(s,{})),o++)}return new Q(r,{})}function Ax(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=hf(new Q([],{}),0,r))}),n}function zD(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function GD(e,n,t){return e==t.path&&wt(n,t.parameters)}var za="imperative",pe=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(pe||{}),Ge=class{id;url;constructor(n,t){this.id=n,this.url=t}},Mt=class extends Ge{type=pe.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},Je=class extends Ge{urlAfterRedirects;type=pe.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},je=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(je||{}),Br=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Br||{}),Et=class extends Ge{reason;code;type=pe.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},St=class extends Ge{reason;code;type=pe.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Ur=class extends Ge{error;target;type=pe.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ti=class extends Ge{urlAfterRedirects;state;type=pe.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},qa=class extends Ge{urlAfterRedirects;state;type=pe.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Za=class extends Ge{urlAfterRedirects;state;shouldActivate;type=pe.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Ya=class extends Ge{urlAfterRedirects;state;type=pe.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Qa=class extends Ge{urlAfterRedirects;state;type=pe.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ka=class{route;type=pe.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Xa=class{route;type=pe.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ja=class{snapshot;type=pe.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ec=class{snapshot;type=pe.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},tc=class{snapshot;type=pe.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},nc=class{snapshot;type=pe.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Hr=class{routerEvent;position;anchor;type=pe.Scroll;constructor(n,t,r){this.routerEvent=n,this.position=t,this.anchor=r}toString(){let n=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${n}')`}},ni=class{},$r=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function Rx(e,n){return e.providers&&!e._injector&&(e._injector=Ro(e.providers,n,`Route: ${e.path}`)),e._injector??n}function lt(e){return e.outlet||F}function Nx(e,n){let t=e.filter(r=>lt(r)===n);return t.push(...e.filter(r=>lt(r)!==n)),t}function li(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var rc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return li(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Tt(this.rootInjector)}},Tt=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new rc(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(R(ie))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),oc=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=pf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=pf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=gf(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return gf(n,this._root).map(t=>t.value)}};function pf(e,n){if(e===n.value)return n;for(let t of n.children){let r=pf(e,t);if(r)return r}return null}function gf(e,n){if(e===n.value)return[n];for(let t of n.children){let r=gf(e,t);if(r.length)return r.unshift(n),r}return[]}var ze=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Lr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var ri=class extends oc{snapshot;constructor(n,t){super(n),this.snapshot=t,wf(this,n)}toString(){return this.snapshot.toString()}};function dI(e){let n=Ox(e),t=new de([new sn("",{})]),r=new de({}),o=new de({}),i=new de({}),s=new de(""),a=new Te(t,r,i,s,o,F,e,n.root);return a.snapshot=n.root,new ri(new ze(a,[]),n)}function Ox(e){let n={},t={},r={},o="",i=new jn([],n,r,o,t,F,e,null,{});return new oi("",new ze(i,[]))}var Te=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(U(l=>l[ci]))??O(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(U(n=>Vn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(U(n=>Vn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function ic(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:I(I({},n.params),e.params),data:I(I({},n.data),e.data),resolve:I(I(I(I({},e.data),n.data),o?.data),e._resolvedData)}:r={params:I({},e.params),data:I({},e.data),resolve:I(I({},e.data),e._resolvedData??{})},o&&hI(o)&&(r.resolve[ci]=o.title),r}var jn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[ci]}constructor(n,t,r,o,i,s,a,c,l){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Vn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Vn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},oi=class extends oc{url;constructor(n,t){super(t),this.url=n,wf(this,t)}toString(){return fI(this._root)}};function wf(e,n){n.value._routerState=e,n.children.forEach(t=>wf(e,t))}function fI(e){let n=e.children.length>0?` { ${e.children.map(fI).join(", ")} } `:"";return`${e.value}${n}`}function af(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,wt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),wt(n.params,t.params)||e.paramsSubject.next(t.params),ax(n.url,t.url)||e.urlSubject.next(t.url),wt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function mf(e,n){let t=wt(e.params,n.params)&&dx(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||mf(e.parent,n.parent))}function hI(e){return typeof e.title=="string"||e.title===null}var pI=new A(""),Ef=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=F;activateEvents=new oe;deactivateEvents=new oe;attachEvents=new oe;detachEvents=new oe;routerOutletData=Rv(void 0);parentContexts=v(Tt);location=v(Ue);changeDetector=v(D);inputBinder=v(ui,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new x(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new x(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new x(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new x(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new vf(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Xe]})}return e})(),vf=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Te?this.route:n===Tt?this.childContexts:n===pI?this.outletData:this.parent.get(n,t)}},ui=new A(""),_f=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=yn([r.queryParams,r.params,r.data]).pipe(ve(([i,s,a],c)=>(a=I(I(I({},i),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=Ca(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function kx(e,n,t){let r=ii(e,n._root,t?t._root:void 0);return new ri(r,n)}function ii(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=Fx(e,n,t);return new ze(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>ii(e,a)),s}}let r=Px(n.value),o=n.children.map(i=>ii(e,i));return new ze(r,o)}}function Fx(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return ii(e,r,o);return ii(e,r)})}function Px(e){return new Te(new de(e.url),new de(e.params),new de(e.queryParams),new de(e.fragment),new de(e.data),e.outlet,e.component,e)}var zr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},gI="ngNavigationCancelingError";function sc(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=cn(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=mI(!1,je.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function mI(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[gI]=!0,t.cancellationCode=n,t}function Lx(e){return vI(e)&&cn(e.url)}function vI(e){return!!e&&e[gI]}var jx=(e,n,t,r)=>U(o=>(new yf(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),yf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),af(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Lr(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Lr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Lr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Lr(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new nc(i.value.snapshot))}),n.children.length&&this.forwardEvent(new ec(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(af(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),af(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},ac=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Vr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function Vx(e,n,t){let r=e._root,o=n?n._root:null;return Ko(r,o,t,[r.value])}function Bx(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Wr(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!Sm(e)?e:n.get(e):r}function Ko(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Lr(n);return e.children.forEach(s=>{Ux(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Jo(a,t.getContext(s),o)),o}function Ux(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=Hx(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new ac(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Ko(e,n,a?a.children:null,r,o):Ko(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Vr(a.outlet.component,s))}else s&&Jo(n,a,o),o.canActivateChecks.push(new ac(r)),i.component?Ko(e,null,a?a.children:null,r,o):Ko(e,null,t,r,o);return o}function Hx(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!Ln(e.url,n.url);case"pathParamsOrQueryParamsChange":return!Ln(e.url,n.url)||!wt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!mf(e,n)||!wt(e.queryParams,n.queryParams);case"paramsChange":default:return!mf(e,n)}}function Jo(e,n,t){let r=Lr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?Jo(s,n.children.getContext(i),t):Jo(s,null,t):Jo(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Vr(n.outlet.component,o)):t.canDeactivateChecks.push(new Vr(null,o)):t.canDeactivateChecks.push(new Vr(null,o))}function di(e){return typeof e=="function"}function $x(e){return typeof e=="boolean"}function zx(e){return e&&di(e.canLoad)}function Gx(e){return e&&di(e.canActivate)}function Wx(e){return e&&di(e.canActivateChild)}function qx(e){return e&&di(e.canDeactivate)}function Zx(e){return e&&di(e.canMatch)}function yI(e){return e instanceof et||e?.name==="EmptyError"}var Ba=Symbol("INITIAL_VALUE");function Gr(){return ve(e=>yn(e.map(n=>n.pipe(At(1),cl(Ba)))).pipe(U(n=>{for(let t of n)if(t!==!0){if(t===Ba)return Ba;if(t===!1||Yx(t))return t}return!0}),me(n=>n!==Ba),At(1)))}function Yx(e){return cn(e)||e instanceof zr}function Qx(e,n){return ae(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?O(P(I({},t),{guardsResult:!0})):Kx(s,r,o,e).pipe(ae(a=>a&&$x(a)?Xx(r,i,e,n):O(a)),U(a=>P(I({},t),{guardsResult:a})))})}function Kx(e,n,t,r){return ne(e).pipe(ae(o=>rA(o.component,o.route,t,n,r)),Rt(o=>o!==!0,!0))}function Xx(e,n,t,r){return ne(n).pipe(dt(o=>rr(eA(o.route.parent,r),Jx(o.route,r),nA(e,o.path,t),tA(e,o.route,t))),Rt(o=>o!==!0,!0))}function Jx(e,n){return e!==null&&n&&n(new tc(e)),O(!0)}function eA(e,n){return e!==null&&n&&n(new Ja(e)),O(!0)}function tA(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return O(!0);let o=r.map(i=>Ji(()=>{let s=li(n)??t,a=Wr(i,s),c=Gx(a)?a.canActivate(n,e):Fe(s,()=>a(n,e));return ln(c).pipe(Rt())}));return O(o).pipe(Gr())}function nA(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>Bx(s)).filter(s=>s!==null).map(s=>Ji(()=>{let a=s.guards.map(c=>{let l=li(s.node)??t,d=Wr(c,l),h=Wx(d)?d.canActivateChild(r,e):Fe(l,()=>d(r,e));return ln(h).pipe(Rt())});return O(a).pipe(Gr())}));return O(i).pipe(Gr())}function rA(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return O(!0);let s=i.map(a=>{let c=li(n)??o,l=Wr(a,c),d=qx(l)?l.canDeactivate(e,n,t,r):Fe(c,()=>l(e,n,t,r));return ln(d).pipe(Rt())});return O(s).pipe(Gr())}function oA(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return O(!0);let i=o.map(s=>{let a=Wr(s,e),c=zx(a)?a.canLoad(n,t):Fe(e,()=>a(n,t));return ln(c)});return O(i).pipe(Gr(),DI(r))}function DI(e){return Xc(Ce(n=>{if(typeof n!="boolean")throw sc(e,n)}),U(n=>n===!0))}function iA(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return O(!0);let i=o.map(s=>{let a=Wr(s,e),c=Zx(a)?a.canMatch(n,t):Fe(e,()=>a(n,t));return ln(c)});return O(i).pipe(Gr(),DI(r))}var si=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},ai=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function Pr(e){return er(new si(e))}function sA(e){return er(new x(4e3,!1))}function aA(e){return er(mI(!1,je.GuardRejected))}var Df=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return O(r);if(o.numberOfChildren>1||!o.children[F])return sA(`${n.redirectTo}`);o=o.children[F]}}applyRedirectCommands(n,t,r,o,i){if(typeof t!="string"){let a=t,{queryParams:c,fragment:l,routeConfig:d,url:h,outlet:p,params:f,data:y,title:S}=o,N=Fe(i,()=>a({params:f,data:y,queryParams:c,fragment:l,routeConfig:d,url:h,outlet:p,title:S}));if(N instanceof _t)throw new ai(N);t=N}let s=this.applyRedirectCreateUrlTree(t,this.urlSerializer.parse(t),n,r);if(t[0]==="/")throw new ai(s);return s}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new _t(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new Q(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new x(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}},If={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function cA(e,n,t,r,o){let i=II(e,n,t);return i.matched?(r=Rx(n,r),iA(r,n,t,o).pipe(U(s=>s===!0?i:I({},If)))):O(i)}function II(e,n,t){if(n.path==="**")return lA(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?I({},If):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||YD)(t,e,n);if(!o)return I({},If);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?I(I({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function lA(e){return{matched:!0,parameters:e.length>0?KD(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function WD(e,n,t,r){return t.length>0&&fA(e,t,r)?{segmentGroup:new Q(n,dA(r,new Q(t,e.children))),slicedSegments:[]}:t.length===0&&hA(e,t,r)?{segmentGroup:new Q(e.segments,uA(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new Q(e.segments,e.children),slicedSegments:t}}function uA(e,n,t,r){let o={};for(let i of t)if(lc(e,n,i)&&!r[lt(i)]){let s=new Q([],{});o[lt(i)]=s}return I(I({},r),o)}function dA(e,n){let t={};t[F]=n;for(let r of e)if(r.path===""&&lt(r)!==F){let o=new Q([],{});t[lt(r)]=o}return t}function fA(e,n,t){return t.some(r=>lc(e,n,r)&&lt(r)!==F)}function hA(e,n,t){return t.some(r=>lc(e,n,r))}function lc(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function pA(e,n,t){return n.length===0&&!e.children[t]}var Cf=class{};function gA(e,n,t,r,o,i,s="emptyOnly"){return new bf(e,n,t,r,o,s,i).recognize()}var mA=31,bf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Df(this.urlSerializer,this.urlTree)}noMatchError(n){return new x(4002,`'${n.segmentGroup}'`)}recognize(){let n=WD(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(U(({children:t,rootSnapshot:r})=>{let o=new ze(r,t),i=new oi("",o),s=sI(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new jn([],Object.freeze({}),Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),F,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,F,t).pipe(U(r=>({children:r,rootSnapshot:t})),ut(r=>{if(r instanceof ai)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof si?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(U(s=>s instanceof ze?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return ne(i).pipe(dt(s=>{let a=r.children[s],c=Nx(t,s);return this.processSegmentGroup(n,c,a,s,o)}),al((s,a)=>(s.push(...a),s)),Zt(null),sl(),ae(s=>{if(s===null)return Pr(r);let a=CI(s);return vA(a),O(a)}))}processSegment(n,t,r,o,i,s,a){return ne(t).pipe(dt(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(ut(l=>{if(l instanceof si)return O(null);throw l}))),Rt(c=>!!c),ut(c=>{if(yI(c))return pA(r,o,i)?O(new Cf):Pr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return lt(r)!==s&&(s===F||!lc(o,i,r))?Pr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):Pr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:d,positionalParamSegments:h,remainingSegments:p}=II(t,o,i);if(!c)return Pr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>mA&&(this.allowRedirects=!1));let f=new jn(i,l,Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,qD(o),lt(o),o.component??o._loadedComponent??null,o,ZD(o)),y=ic(f,a,this.paramsInheritanceStrategy);f.params=Object.freeze(y.params),f.data=Object.freeze(y.data);let S=this.applyRedirects.applyRedirectCommands(d,o.redirectTo,h,f,n);return this.applyRedirects.lineralizeSegments(o,S).pipe(ae(N=>this.processSegment(n,r,t,N.concat(p),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=cA(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(ve(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(ve(({routes:l})=>{let d=r._loadedInjector??n,{parameters:h,consumedSegments:p,remainingSegments:f}=c,y=new jn(p,h,Object.freeze(I({},this.urlTree.queryParams)),this.urlTree.fragment,qD(r),lt(r),r.component??r._loadedComponent??null,r,ZD(r)),S=ic(y,s,this.paramsInheritanceStrategy);y.params=Object.freeze(S.params),y.data=Object.freeze(S.data);let{segmentGroup:N,slicedSegments:z}=WD(t,p,f,l);if(z.length===0&&N.hasChildren())return this.processChildren(d,l,N,y).pipe(U(ce=>new ze(y,ce)));if(l.length===0&&z.length===0)return O(new ze(y,[]));let xt=lt(r)===i;return this.processSegment(d,l,N,z,xt?F:i,!0,y).pipe(U(ce=>new ze(y,ce instanceof ze?[ce]:[])))}))):Pr(t)))}getChildConfig(n,t,r){return t.children?O({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?O({routes:t._loadedRoutes,injector:t._loadedInjector}):oA(n,t,r,this.urlSerializer).pipe(ae(o=>o?this.configLoader.loadChildren(n,t).pipe(Ce(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):aA(t))):O({routes:[],injector:n})}};function vA(e){e.sort((n,t)=>n.value.outlet===F?-1:t.value.outlet===F?1:n.value.outlet.localeCompare(t.value.outlet))}function yA(e){let n=e.value.routeConfig;return n&&n.path===""}function CI(e){let n=[],t=new Set;for(let r of e){if(!yA(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=CI(r.children);n.push(new ze(r.value,o))}return n.filter(r=>!t.has(r))}function qD(e){return e.data||{}}function ZD(e){return e.resolve||{}}function DA(e,n,t,r,o,i){return ae(s=>gA(e,n,t,r,s.extractedUrl,o,i).pipe(U(({state:a,tree:c})=>P(I({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function IA(e,n){return ae(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return O(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of bI(c))s.add(l);let a=0;return ne(s).pipe(dt(c=>i.has(c)?CA(c,r,e,n):(c.data=ic(c,c.parent,e).resolve,O(void 0))),Ce(()=>a++),or(1),ae(c=>a===s.size?O(t):Re))})}function bI(e){let n=e.children.map(t=>bI(t)).flat();return[e,...n]}function CA(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!hI(o)&&(i[ci]=o.title),bA(i,e,n,r).pipe(U(s=>(e._resolvedData=s,e.data=ic(e,e.parent,t).resolve,null)))}function bA(e,n,t,r){let o=uf(e);if(o.length===0)return O({});let i={};return ne(o).pipe(ae(s=>wA(e[s],n,t,r).pipe(Rt(),Ce(a=>{if(a instanceof zr)throw sc(new an,a);i[s]=a}))),or(1),U(()=>i),ut(s=>yI(s)?Re:er(s)))}function wA(e,n,t,r){let o=li(n)??r,i=Wr(e,o),s=i.resolve?i.resolve(n,t):Fe(o,()=>i(n,t));return ln(s)}function cf(e){return ve(n=>{let t=e(n);return t?ne(t).pipe(U(()=>n)):O(n)})}var Mf=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===F);return r}getResolvedTitleForRoute(t){return t.data[ci]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(wI),providedIn:"root"})}return e})(),wI=(()=>{class e extends Mf{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(R(UD))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Bn=new A("",{providedIn:"root",factory:()=>({})}),Sf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=C({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Fd(0,"router-outlet")},dependencies:[Ef],encapsulation:2})}return e})();function Tf(e){let n=e.children&&e.children.map(Tf),t=n?P(I({},e),{children:n}):I({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==F&&(t.component=Sf),t}var qr=new A(""),uc=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=v(Ia);loadComponent(t){if(this.componentLoaders.get(t))return this.componentLoaders.get(t);if(t._loadedComponent)return O(t._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(t);let r=ln(t.loadComponent()).pipe(U(_I),Ce(i=>{this.onLoadEndListener&&this.onLoadEndListener(t),t._loadedComponent=i}),Yt(()=>{this.componentLoaders.delete(t)})),o=new Xn(r,()=>new X).pipe(Kn());return this.componentLoaders.set(t,o),o}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return O({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=EI(r,this.compiler,t,this.onLoadEndListener).pipe(Yt(()=>{this.childrenLoaders.delete(r)})),s=new Xn(i,()=>new X).pipe(Kn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function EI(e,n,t,r){return ln(e.loadChildren()).pipe(U(_I),ae(o=>o instanceof Ad||Array.isArray(o)?O(o):ne(n.compileModuleAsync(o))),U(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(qr,[],{optional:!0,self:!0}).flat()),{routes:s.map(Tf),injector:i}}))}function EA(e){return e&&typeof e=="object"&&"default"in e}function _I(e){return EA(e)?e.default:e}var dc=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(_A),providedIn:"root"})}return e})(),_A=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),xf=new A(""),Af=new A("");function MI(e,n,t){let r=e.get(Af),o=e.get(se);return e.get(g).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),MA(e))),{onViewTransitionCreated:c}=r;return c&&Fe(e,()=>c({transition:a,from:n,to:t})),s})}function MA(e){return new Promise(n=>{ld({read:()=>setTimeout(n)},{injector:e})})}var Rf=new A(""),fc=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new X;transitionAbortSubject=new X;configLoader=v(uc);environmentInjector=v(ie);destroyRef=v(_o);urlSerializer=v(Ht);rootContexts=v(Tt);location=v($e);inputBindingEnabled=v(ui,{optional:!0})!==null;titleStrategy=v(Mf);options=v(Bn,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=v(dc);createViewTransition=v(xf,{optional:!0});navigationErrorHandler=v(Rf,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>O(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new Ka(o)),r=o=>this.events.next(new Xa(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(P(I({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,id:r}))}setupNavigations(t){return this.transitions=new de(null),this.transitions.pipe(me(r=>r!==null),ve(r=>{let o=!1,i=!1;return O(r).pipe(ve(s=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",je.SupersededByNewNavigation),Re;this.currentTransition=r,this.currentNavigation={id:s.id,initialUrl:s.rawUrl,extractedUrl:s.extractedUrl,targetBrowserUrl:typeof s.extras.browserUrl=="string"?this.urlSerializer.parse(s.extras.browserUrl):s.extras.browserUrl,trigger:s.source,extras:s.extras,previousNavigation:this.lastSuccessfulNavigation?P(I({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let a=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),c=s.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!a&&c!=="reload"){let l="";return this.events.next(new St(s.id,this.urlSerializer.serialize(s.rawUrl),l,Br.IgnoredSameUrlNavigation)),s.resolve(!1),Re}if(this.urlHandlingStrategy.shouldProcessUrl(s.rawUrl))return O(s).pipe(ve(l=>(this.events.next(new Mt(l.id,this.urlSerializer.serialize(l.extractedUrl),l.source,l.restoredState)),l.id!==this.navigationId?Re:Promise.resolve(l))),DA(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),Ce(l=>{r.targetSnapshot=l.targetSnapshot,r.urlAfterRedirects=l.urlAfterRedirects,this.currentNavigation=P(I({},this.currentNavigation),{finalUrl:l.urlAfterRedirects});let d=new ti(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(d)}));if(a&&this.urlHandlingStrategy.shouldProcessUrl(s.currentRawUrl)){let{id:l,extractedUrl:d,source:h,restoredState:p,extras:f}=s,y=new Mt(l,this.urlSerializer.serialize(d),h,p);this.events.next(y);let S=dI(this.rootComponentType).snapshot;return this.currentTransition=r=P(I({},s),{targetSnapshot:S,urlAfterRedirects:d,extras:P(I({},f),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=d,O(r)}else{let l="";return this.events.next(new St(s.id,this.urlSerializer.serialize(s.extractedUrl),l,Br.IgnoredByUrlHandlingStrategy)),s.resolve(!1),Re}}),Ce(s=>{let a=new qa(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),U(s=>(this.currentTransition=r=P(I({},s),{guards:Vx(s.targetSnapshot,s.currentSnapshot,this.rootContexts)}),r)),Qx(this.environmentInjector,s=>this.events.next(s)),Ce(s=>{if(r.guardsResult=s.guardsResult,s.guardsResult&&typeof s.guardsResult!="boolean")throw sc(this.urlSerializer,s.guardsResult);let a=new Za(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot,!!s.guardsResult);this.events.next(a)}),me(s=>s.guardsResult?!0:(this.cancelNavigationTransition(s,"",je.GuardRejected),!1)),cf(s=>{if(s.guards.canActivateChecks.length!==0)return O(s).pipe(Ce(a=>{let c=new Ya(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}),ve(a=>{let c=!1;return O(a).pipe(IA(this.paramsInheritanceStrategy,this.environmentInjector),Ce({next:()=>c=!0,complete:()=>{c||this.cancelNavigationTransition(a,"",je.NoDataFromResolver)}}))}),Ce(a=>{let c=new Qa(a.id,this.urlSerializer.serialize(a.extractedUrl),this.urlSerializer.serialize(a.urlAfterRedirects),a.targetSnapshot);this.events.next(c)}))}),cf(s=>{let a=c=>{let l=[];c.routeConfig?.loadComponent&&!c.routeConfig._loadedComponent&&l.push(this.configLoader.loadComponent(c.routeConfig).pipe(Ce(d=>{c.component=d}),U(()=>{})));for(let d of c.children)l.push(...a(d));return l};return yn(a(s.targetSnapshot.root)).pipe(Zt(null),At(1))}),cf(()=>this.afterPreactivation()),ve(()=>{let{currentSnapshot:s,targetSnapshot:a}=r,c=this.createViewTransition?.(this.environmentInjector,s.root,a.root);return c?ne(c).pipe(U(()=>r)):O(r)}),U(s=>{let a=kx(t.routeReuseStrategy,s.targetSnapshot,s.currentRouterState);return this.currentTransition=r=P(I({},s),{targetRouterState:a}),this.currentNavigation.targetRouterState=a,r}),Ce(()=>{this.events.next(new ni)}),jx(this.rootContexts,t.routeReuseStrategy,s=>this.events.next(s),this.inputBindingEnabled),At(1),Ce({next:s=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Je(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects))),this.titleStrategy?.updateTitle(s.targetRouterState.snapshot),s.resolve(!0)},complete:()=>{o=!0}}),ll(this.transitionAbortSubject.pipe(Ce(s=>{throw s}))),Yt(()=>{!o&&!i&&this.cancelNavigationTransition(r,"",je.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),ut(s=>{if(this.destroyed)return r.resolve(!1),Re;if(i=!0,vI(s))this.events.next(new Et(r.id,this.urlSerializer.serialize(r.extractedUrl),s.message,s.cancellationCode)),Lx(s)?this.events.next(new $r(s.url,s.navigationBehaviorOptions)):r.resolve(!1);else{let a=new Ur(r.id,this.urlSerializer.serialize(r.extractedUrl),s,r.targetSnapshot??void 0);try{let c=Fe(this.environmentInjector,()=>this.navigationErrorHandler?.(a));if(c instanceof zr){let{message:l,cancellationCode:d}=sc(this.urlSerializer,c);this.events.next(new Et(r.id,this.urlSerializer.serialize(r.extractedUrl),l,d)),this.events.next(new $r(c.redirectTo,c.navigationBehaviorOptions))}else throw this.events.next(a),s}catch(c){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(c)}}return Re}))}))}cancelNavigationTransition(t,r,o){let i=new Et(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function SA(e){return e!==za}var SI=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(TA),providedIn:"root"})}return e})(),cc=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},TA=(()=>{class e extends cc{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),TI=(()=>{class e{urlSerializer=v(Ht);options=v(Bn,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=v($e);urlHandlingStrategy=v(dc);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new _t;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof _t?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=dI(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:()=>v(xA),providedIn:"root"})}return e})(),xA=(()=>{class e extends TI{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof Mt?this.updateStateMemento():t instanceof St?this.commitTransition(r):t instanceof ti?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof ni?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Et&&(t.code===je.GuardRejected||t.code===je.NoDataFromResolver)?this.restoreHistory(r):t instanceof Ur?this.restoreHistory(r,!0):t instanceof Je&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=I(I({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=I(I({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function hc(e,n){e.events.pipe(me(t=>t instanceof Je||t instanceof Et||t instanceof Ur||t instanceof St),U(t=>t instanceof Je||t instanceof St?0:(t instanceof Et?t.code===je.Redirect||t.code===je.SupersededByNewNavigation:!1)?2:1),me(t=>t!==2),At(1)).subscribe(()=>{n()})}var AA={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},RA={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},De=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=v(Hy);stateManager=v(TI);options=v(Bn,{optional:!0})||{};pendingTasks=v(jt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=v(fc);urlSerializer=v(Ht);location=v($e);urlHandlingStrategy=v(dc);_events=new X;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=v(SI);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=v(qr,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!v(ui,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new le;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Et&&r.code!==je.Redirect&&r.code!==je.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof Je)this.navigated=!0;else if(r instanceof $r){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=I({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||SA(o.source)},s);this.scheduleNavigation(a,za,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}OA(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),za,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=I({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Tf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,d=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":d=I(I({},this.currentUrlTree.queryParams),i);break;case"preserve":d=this.currentUrlTree.queryParams;break;default:d=i||null}d!==null&&(d=this.removeEmptyProps(d));let h;try{let p=o?o.snapshot:this.routerState.snapshot.root;h=aI(p)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),h=this.currentUrlTree.root}return cI(h,t,d,l??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=cn(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,za,null,r)}navigate(t,r={skipLocationChange:!1}){return NA(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=I({},AA):r===!1?o=I({},RA):o=r,cn(t))return HD(this.currentUrlTree,t,o);let i=this.parseUrl(t);return HD(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((h,p)=>{a=h,c=p});let d=this.pendingTasks.add();return hc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(d))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(h=>Promise.reject(h))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function NA(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new x(4008,!1)}function OA(e){return!(e instanceof ni)&&!(e instanceof $r)}var hi=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;href=null;target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new X;constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=t.events.subscribe(l=>{l instanceof Je&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(cn(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.href=t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t)):null;let r=this.href===null?null:Jv(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:cn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(u(De),u(Te),Ct("tabindex"),u(en),u(m),u(Le))};static \u0275dir=V({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&ot("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",rn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",rn],replaceUrl:[2,"replaceUrl","replaceUrl",rn],routerLink:"routerLink"},features:[Xe]})}return e})();var fi=class{},kA=(()=>{class e{preload(t,r){return r().pipe(ut(()=>O(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var xI=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i,s){this.router=t,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(me(t=>t instanceof Je),dt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Ro(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return ne(o).pipe(nr())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=O(null);let i=o.pipe(ae(s=>s===null?O(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return ne([i,s]).pipe(nr())}else return i})}static \u0275fac=function(r){return new(r||e)(R(De),R(Ia),R(ie),R(fi),R(uc))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),AI=new A(""),FA=(()=>{class e{urlSerializer;transitions;viewportScroller;zone;options;routerEventsSubscription;scrollEventsSubscription;lastId=0;lastSource="imperative";restoredId=0;store={};constructor(t,r,o,i,s={}){this.urlSerializer=t,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Mt?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof Je?(this.lastId=t.id,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.urlAfterRedirects).fragment)):t instanceof St&&t.code===Br.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(t,this.urlSerializer.parse(t.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(t=>{t instanceof Hr&&(t.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(t.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Hr(t,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static \u0275fac=function(r){xy()};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();function PA(e){return e.routerState.root}function pi(e,n){return{\u0275kind:e,\u0275providers:n}}function LA(){let e=v(J);return n=>{let t=e.get(Dt);if(n!==t.components[0])return;let r=e.get(De),o=e.get(RI);e.get(Of)===1&&r.initialNavigation(),e.get(kI,null,H.Optional)?.setUpPreloading(),e.get(AI,null,H.Optional)?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var RI=new A("",{factory:()=>new X}),Of=new A("",{providedIn:"root",factory:()=>1});function NI(){let e=[{provide:Of,useValue:0},Od(()=>{let n=v(J);return n.get(Bd,Promise.resolve()).then(()=>new Promise(r=>{let o=n.get(De),i=n.get(RI);hc(o,()=>{r(!0)}),n.get(fc).afterPreactivation=()=>(r(!0),i.closed?O(void 0):i),o.initialNavigation()}))})];return pi(2,e)}function OI(){let e=[Od(()=>{v(De).setUpLocationChangeListener()}),{provide:Of,useValue:2}];return pi(3,e)}var kI=new A("");function FI(e){return pi(0,[{provide:kI,useExisting:xI},{provide:fi,useExisting:e}])}function PI(){return pi(8,[_f,{provide:ui,useExisting:_f}])}function LI(e){So("NgRouterViewTransitions");let n=[{provide:xf,useValue:MI},{provide:Af,useValue:I({skipNextTransition:!!e?.skipInitialTransition},e)}];return pi(9,n)}var jI=[$e,{provide:Ht,useClass:an},De,Tt,{provide:Te,useFactory:PA,deps:[De]},uc,[]],jA=(()=>{class e{constructor(){}static forRoot(t,r){return{ngModule:e,providers:[jI,[],{provide:qr,multi:!0,useValue:t},[],r?.errorHandler?{provide:Rf,useValue:r.errorHandler}:[],{provide:Bn,useValue:r||{}},r?.useHash?BA():UA(),VA(),r?.preloadingStrategy?FI(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?HA(r):[],r?.bindToComponentInputs?PI().\u0275providers:[],r?.enableViewTransitions?LI().\u0275providers:[],$A()]}}static forChild(t){return{ngModule:e,providers:[{provide:qr,multi:!0,useValue:t}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({})}return e})();function VA(){return{provide:AI,useFactory:()=>{let e=v(gD),n=v(g),t=v(Bn),r=v(fc),o=v(Ht);return t.scrollOffset&&e.setOffset(t.scrollOffset),new FA(o,r,e,n,t)}}}function BA(){return{provide:Le,useClass:Hd}}function UA(){return{provide:Le,useClass:Ea}}function HA(e){return[e.initialNavigation==="disabled"?OI().\u0275providers:[],e.initialNavigation==="enabledBlocking"?NI().\u0275providers:[]]}var Nf=new A("");function $A(){return[{provide:Nf,useFactory:LA},{provide:kd,multi:!0,useExisting:Nf}]}var ZI=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(u(en),u(m))};static \u0275dir=V({type:e})}return e})(),zA=(()=>{class e extends ZI{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,features:[ee]})}return e})(),Hn=new A("");var GA={provide:Hn,useExisting:Be(()=>YI),multi:!0};function WA(){let e=He()?He().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var qA=new A(""),YI=(()=>{class e extends ZI{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!WA())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(u(en),u(m),u(qA,8))};static \u0275dir=V({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&ye("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Ae([GA]),ee]})}return e})();function ZA(e){return e==null||QI(e)===0}function QI(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var $t=new A(""),KI=new A("");function YA(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function QA(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function KA(e){return ZA(e.value)?{required:!0}:null}function XA(e){return n=>{let t=n.value?.length??QI(n.value);return t!==null&&t>e?{maxlength:{requiredLength:e,actualLength:t}}:null}}function BI(e){return null}function XI(e){return e!=null}function JI(e){return kn(e)?ne(e):e}function eC(e){let n={};return e.forEach(t=>{n=t!=null?I(I({},n),t):n}),Object.keys(n).length===0?null:n}function tC(e,n){return n.map(t=>t(e))}function JA(e){return!e.validate}function nC(e){return e.map(n=>JA(n)?n:t=>n.validate(t))}function eR(e){if(!e)return null;let n=e.filter(XI);return n.length==0?null:function(t){return eC(tC(t,n))}}function Ff(e){return e!=null?eR(nC(e)):null}function tR(e){if(!e)return null;let n=e.filter(XI);return n.length==0?null:function(t){let r=tC(t,n).map(JI);return ol(r).pipe(U(eC))}}function Pf(e){return e!=null?tR(nC(e)):null}function UI(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function nR(e){return e._rawValidators}function rR(e){return e._rawAsyncValidators}function kf(e){return e?Array.isArray(e)?e:[e]:[]}function gc(e,n){return Array.isArray(e)?e.includes(n):e===n}function HI(e,n){let t=kf(n);return kf(e).forEach(o=>{gc(t,o)||t.push(o)}),t}function $I(e,n){return kf(n).filter(t=>!gc(e,t))}var mc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Ff(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Pf(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Qr=class extends mc{name;get formDirective(){return null}get path(){return null}},Un=class extends mc{_parent=null;name=null;valueAccessor=null},vc=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},oR={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},az=P(I({},oR),{"[class.ng-submitted]":"isSubmitted"}),cz=(()=>{class e extends vc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(u(Un,2))};static \u0275dir=V({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&ma("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[ee]})}return e})(),lz=(()=>{class e extends vc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(u(Qr,10))};static \u0275dir=V({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&ma("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[ee]})}return e})();var gi="VALID",pc="INVALID",Zr="PENDING",mi="DISABLED",Kr=class{},yc=class extends Kr{value;source;constructor(n,t){super(),this.value=n,this.source=t}},yi=class extends Kr{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},Di=class extends Kr{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Yr=class extends Kr{status;source;constructor(n,t){super(),this.status=n,this.source=t}};function rC(e){return(Cc(e)?e.validators:e)||null}function iR(e){return Array.isArray(e)?Ff(e):e||null}function oC(e,n){return(Cc(n)?n.asyncValidators:e)||null}function sR(e){return Array.isArray(e)?Pf(e):e||null}function Cc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function aR(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new x(1e3,"");if(!r[t])throw new x(1001,"")}function cR(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new x(1002,"")})}var Dc=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Bt(this.statusReactive)}set status(n){Bt(()=>this.statusReactive.set(n))}_status=Po(()=>this.statusReactive());statusReactive=Mo(void 0);get valid(){return this.status===gi}get invalid(){return this.status===pc}get pending(){return this.status==Zr}get disabled(){return this.status===mi}get enabled(){return this.status!==mi}errors;get pristine(){return Bt(this.pristineReactive)}set pristine(n){Bt(()=>this.pristineReactive.set(n))}_pristine=Po(()=>this.pristineReactive());pristineReactive=Mo(!0);get dirty(){return!this.pristine}get touched(){return Bt(this.touchedReactive)}set touched(n){Bt(()=>this.touchedReactive.set(n))}_touched=Po(()=>this.touchedReactive());touchedReactive=Mo(!1);get untouched(){return!this.touched}_events=new X;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(HI(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(HI(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators($I(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators($I(n,this._rawAsyncValidators))}hasValidator(n){return gc(this._rawValidators,n)}hasAsyncValidator(n){return gc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(P(I({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new Di(!0,r))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new Di(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(P(I({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new yi(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new yi(!0,r))}markAsPending(n={}){this.status=Zr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Yr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(P(I({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=mi,this.errors=null,this._forEachChild(o=>{o.disable(P(I({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new yc(this.value,r)),this._events.next(new Yr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(P(I({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=gi,this._forEachChild(r=>{r.enable(P(I({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(P(I({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===gi||this.status===Zr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new yc(this.value,t)),this._events.next(new Yr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(P(I({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?mi:gi}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=Zr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1};let r=JI(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Yr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new oe,this.statusChanges=new oe}_calculateStatus(){return this._allControlsDisabled()?mi:this.errors?pc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Zr)?Zr:this._anyControlsHaveStatus(pc)?pc:gi}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new yi(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new Di(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Cc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=iR(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=sR(this._rawAsyncValidators)}},Ic=class extends Dc{constructor(n,t,r){super(rC(t),oC(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){cR(this,!0,n),Object.keys(n).forEach(r=>{aR(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var Lf=new A("",{providedIn:"root",factory:()=>jf}),jf="always";function lR(e,n){return[...n.path,e]}function iC(e,n,t=jf){sC(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),dR(e,n),hR(e,n),fR(e,n),uR(e,n)}function zI(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function uR(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function sC(e,n){let t=nR(e);n.validator!==null?e.setValidators(UI(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=rR(e);n.asyncValidator!==null?e.setAsyncValidators(UI(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();zI(n._rawValidators,o),zI(n._rawAsyncValidators,o)}function dR(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&aC(e,n)})}function fR(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&aC(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function aC(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function hR(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function pR(e,n){e==null,sC(e,n)}function gR(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function mR(e){return Object.getPrototypeOf(e.constructor)===zA}function vR(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function yR(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===YI?t=i:mR(i)?r=i:o=i}),o||r||t||null}var DR={provide:Qr,useExisting:Be(()=>IR)},vi=Promise.resolve(),IR=(()=>{class e extends Qr{callSetDisabledState;get submitted(){return Bt(this.submittedReactive)}_submitted=Po(()=>this.submittedReactive());submittedReactive=Mo(!1);_directives=new Set;form;ngSubmit=new oe;options;constructor(t,r,o){super(),this.callSetDisabledState=o,this.form=new Ic({},Ff(t),Pf(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(t){vi.then(()=>{let r=this._findContainer(t.path);t.control=r.registerControl(t.name,t.control),iC(t.control,t,this.callSetDisabledState),t.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(t)})}getControl(t){return this.form.get(t.path)}removeControl(t){vi.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name),this._directives.delete(t)})}addFormGroup(t){vi.then(()=>{let r=this._findContainer(t.path),o=new Ic({});pR(o,t),r.registerControl(t.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(t){vi.then(()=>{let r=this._findContainer(t.path);r&&r.removeControl(t.name)})}getFormGroup(t){return this.form.get(t.path)}updateModel(t,r){vi.then(()=>{this.form.get(t.path).setValue(r)})}setValue(t){this.control.setValue(t)}onSubmit(t){return this.submittedReactive.set(!0),vR(this.form,this._directives),this.ngSubmit.emit(t),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0){this.form.reset(t),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(t){return t.pop(),t.length?this.form.get(t):this.form}static \u0275fac=function(r){return new(r||e)(u($t,10),u(KI,10),u(Lf,8))};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&ye("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Ae([DR]),ee]})}return e})();function GI(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function WI(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var CR=class extends Dc{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(rC(t),oC(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Cc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(WI(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){GI(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){GI(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){WI(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var bR={provide:Un,useExisting:Be(()=>wR)},qI=Promise.resolve(),wR=(()=>{class e extends Un{_changeDetectorRef;callSetDisabledState;control=new CR;static ngAcceptInputType_isDisabled;_registered=!1;viewModel;name="";isDisabled;model;options;update=new oe;constructor(t,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=yR(this,i)}ngOnChanges(t){if(this._checkForErrors(),!this._registered||"name"in t){if(this._registered&&(this._checkName(),this.formDirective)){let r=t.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in t&&this._updateDisabled(t),gR(t,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){iC(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._checkName()}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(t){qI.then(()=>{this.control.setValue(t,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(t){let r=t.isDisabled.currentValue,o=r!==0&&rn(r);qI.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(t){return this._parent?lR(t,this._parent):[t]}static \u0275fac=function(r){return new(r||e)(u(Qr,9),u($t,10),u(KI,10),u(Hn,10),u(D,8),u(Lf,8))};static \u0275dir=V({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"],options:[0,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],standalone:!1,features:[Ae([bR]),ee,Xe]})}return e})();var dz=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})();function ER(e){return typeof e=="number"?e:parseInt(e,10)}function cC(e){return typeof e=="number"?e:parseFloat(e)}var bc=(()=>{class e{_validator=BI;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):BI,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=V({type:e,features:[Xe]})}return e})(),_R={provide:$t,useExisting:Be(()=>Vf),multi:!0},Vf=(()=>{class e extends bc{max;inputName="max";normalizeInput=t=>cC(t);createValidator=t=>QA(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[Ae([_R]),ee]})}return e})(),MR={provide:$t,useExisting:Be(()=>Bf),multi:!0},Bf=(()=>{class e extends bc{min;inputName="min";normalizeInput=t=>cC(t);createValidator=t=>YA(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[Ae([MR]),ee]})}return e})(),SR={provide:$t,useExisting:Be(()=>TR),multi:!0};var TR=(()=>{class e extends bc{required;inputName="required";normalizeInput=rn;createValidator=t=>KA;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&ot("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Ae([SR]),ee]})}return e})();var xR={provide:$t,useExisting:Be(()=>AR),multi:!0},AR=(()=>{class e extends bc{maxlength;inputName="maxlength";normalizeInput=t=>ER(t);createValidator=t=>XA(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("maxlength",o._enabled?o.maxlength:null)},inputs:{maxlength:"maxlength"},standalone:!1,features:[Ae([xR]),ee]})}return e})();var RR=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({})}return e})();var fz=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Lf,useValue:t.callSetDisabledState??jf}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({imports:[RR]})}return e})();var wc=e=>NR(e),Ii=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),wc(e).includes(n)),NR=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=OR(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},OR=e=>{let n=Wn.get("platform");return Object.keys(lC).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):lC[t](e)})},kR=e=>Ec(e)&&!dC(e),Uf=e=>!!($n(e,/iPad/i)||$n(e,/Macintosh/i)&&Ec(e)),FR=e=>$n(e,/iPhone/i),PR=e=>$n(e,/iPhone|iPod/i)||Uf(e),uC=e=>$n(e,/android|sink/i),LR=e=>uC(e)&&!$n(e,/mobile/i),jR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},VR=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return Uf(e)||LR(e)||r>460&&r<820&&o>780&&o<1400},Ec=e=>$R(e,"(any-pointer:coarse)"),BR=e=>!Ec(e),dC=e=>fC(e)||hC(e),fC=e=>!!(e.cordova||e.phonegap||e.PhoneGap),hC=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},UR=e=>$n(e,/electron/i),HR=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},$n=(e,n)=>n.test(e.navigator.userAgent),$R=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},lC={ipad:Uf,iphone:FR,ios:PR,android:uC,phablet:jR,tablet:VR,cordova:fC,capacitor:hC,electron:UR,pwa:HR,mobile:Ec,mobileweb:kR,desktop:BR,hybrid:dC},zR,Hf=e=>e&&Bp(e)||zR;var wz=e=>{try{if(e instanceof Gf)return e.value;if(!GR()||typeof e!="string"||e==="")return e;if(e.includes("onload="))return"";let n=document.createDocumentFragment(),t=document.createElement("div");n.appendChild(t),t.innerHTML=e,qR.forEach(s=>{let a=n.querySelectorAll(s);for(let c=a.length-1;c>=0;c--){let l=a[c];l.parentNode?l.parentNode.removeChild(l):n.removeChild(l);let d=zf(l);for(let h=0;h<d.length;h++)$f(d[h])}});let r=zf(n);for(let s=0;s<r.length;s++)$f(r[s]);let o=document.createElement("div");o.appendChild(n);let i=o.querySelector("div");return i!==null?i.innerHTML:o.innerHTML}catch(n){return Si("sanitizeDOMString",n),""}},$f=e=>{if(e.nodeType&&e.nodeType!==1)return;if(typeof NamedNodeMap<"u"&&!(e.attributes instanceof NamedNodeMap)){e.remove();return}for(let t=e.attributes.length-1;t>=0;t--){let r=e.attributes.item(t),o=r.name;if(!WR.includes(o.toLowerCase())){e.removeAttribute(o);continue}let i=r.value,s=e[o];(i!=null&&i.toLowerCase().includes("javascript:")||s!=null&&s.toLowerCase().includes("javascript:"))&&e.removeAttribute(o)}let n=zf(e);for(let t=0;t<n.length;t++)$f(n[t])},zf=e=>e.children!=null?e.children:e.childNodes,GR=()=>{var e;let n=window,t=(e=n?.Ionic)===null||e===void 0?void 0:e.config;return t?t.get?t.get("sanitizerEnabled",!0):t.sanitizerEnabled===!0||t.sanitizerEnabled===void 0:!0},WR=["class","id","href","src","name","slot"],qR=["script","style","iframe","meta","link","object","embed"],Gf=class{constructor(n){this.value=n}};var Ez=!1;var Mz=(e,n)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},n):n,ZR=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],Sz=e=>{let n={};return ZR(e).forEach(t=>n[t]=!0),n};var pC=()=>{let e,n;return{attachViewToDom:(c,l,...d)=>ge(void 0,[c,l,...d],function*(o,i,s={},a=[]){var h,p;e=o;let f;if(i){let S=typeof i=="string"?(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement(i):i;a.forEach(N=>S.classList.add(N)),Object.assign(S,s),e.appendChild(S),f=S,yield new Promise(N=>hn(S,N))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let N=(p=e.ownerDocument)===null||p===void 0?void 0:p.createElement("div");N.classList.add("ion-delegate-host"),a.forEach(z=>N.classList.add(z)),N.append(...e.children),e.appendChild(N),f=N}let y=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),y.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var bi='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',gC=(e,n)=>{let t=e.querySelector(bi);DC(t,n??e)},mC=(e,n)=>{let t=Array.from(e.querySelectorAll(bi)),r=t.length>0?t[t.length-1]:null;DC(r,n??e)},DC=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(bi)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():Nc(t)}else n.focus()},Wf=0,YR=0,_c=new WeakMap,QR=e=>({create(t){return XR(e,t)},dismiss(t,r,o){return nN(document,t,r,e,o)},getTop(){return ge(this,null,function*(){return Ci(document,e)})}});var KR=QR("ion-loading");var Bz=e=>{typeof document<"u"&&tN(document);let n=Wf++;e.overlayIndex=n},Uz=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++YR}`),e.id),XR=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),CC(document).appendChild(t),new Promise(r=>hn(t,r))}):Promise.resolve(),JR=e=>e.classList.contains("overlay-hidden"),vC=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(bi)||e),t?Nc(t):n.focus()},eN=(e,n)=>{let t=Ci(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(lN))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")vC(t.lastFocus,t);else{let s=$p(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;gC(a,t),c===n.activeElement&&mC(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")vC(t.lastFocus,t);else{let s=t.lastFocus;gC(t),s===n.activeElement&&mC(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},tN=e=>{Wf===0&&(Wf=1,e.addEventListener("focus",n=>{eN(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=Ci(e);t?.backdropDismiss&&n.detail.register(Gp,()=>{t.dismiss(void 0,yC)})}),zp()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=Ci(e);t?.backdropDismiss&&t.dismiss(void 0,yC)}}))},nN=(e,n,t,r,o)=>{let i=Ci(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},rN=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),Mc=(e,n)=>rN(e,n).filter(t=>!JR(t)),Ci=(e,n,t)=>{let r=Mc(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},IC=(e=!1)=>{let t=CC(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},Hz=(e,n,t,r,o)=>ge(void 0,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(IC(!0),document.body.classList.add(jc)),aN(e.el),wC(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=Hf(e),c=e.enterAnimation?e.enterAnimation:Wn.get(n,a==="ios"?t:r);(yield bC(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&oN(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),oN=e=>ge(void 0,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(bi)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),$z=(e,n,t,r,o,i,s)=>ge(void 0,null,function*(){var a,c;if(!e.presented)return!1;let d=(zt!==void 0?Mc(zt):[]).filter(p=>p.tagName!=="ION-TOAST");d.length===1&&d[0].id===e.el.id&&(IC(!1),document.body.classList.remove(jc)),e.presented=!1;try{wC(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let p=Hf(e),f=e.leaveAnimation?e.leaveAnimation:Wn.get(r,p==="ios"?o:i);t!==sN&&(yield bC(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(_c.get(e)||[]).forEach(S=>S.destroy()),_c.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(p){Si(`[${e.el.tagName.toLowerCase()}] - `,p)}return e.el.remove(),cN(),!0}),CC=e=>e.querySelector("ion-app")||e.body,bC=(e,n,t,r)=>ge(void 0,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!Wn.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=_c.get(e)||[];return _c.set(e,[...s,i]),yield i.play(),!0}),zz=(e,n)=>{let t,r=new Promise(o=>t=o);return iN(e,n,o=>{t(o.detail)}),r},iN=(e,n,t)=>{let r=o=>{Hp(e,n,r),t(o)};Up(e,n,r)};var yC="backdrop",sN="gesture";var Gz=e=>{let n=!1,t,r=pC(),o=(a=!1)=>{if(t&&!a)return{delegate:t,inline:n};let{el:c,hasController:l,delegate:d}=e;return n=c.parentNode!==null&&!l,t=n?d||r:d,{inline:n,delegate:t}};return{attachViewToDom:a=>ge(void 0,null,function*(){let{delegate:c}=o(!0);if(c)return yield c.attachViewToDom(e.el,a);let{hasController:l}=e;if(l&&a!==void 0)throw new Error("framework delegate is missing");return null}),removeViewFromDom:()=>{let{delegate:a}=o();a&&e.el!==void 0&&a.removeViewFromDom(e.el.parentElement,e.el)}}},Wz=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){Rc(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let l=()=>{c.present()};return a.addEventListener("click",l),()=>{a.removeEventListener("click",l)}})(i,r)},removeClickListener:n}},wC=e=>{zt!==void 0&&Ii("android")&&e.setAttribute("aria-hidden","true")},aN=e=>{var n;if(zt===void 0)return;let t=Mc(zt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},cN=()=>{if(zt===void 0)return;let e=Mc(zt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},lN="ion-disable-focus-trap";var uN=["tabsInner"];var SC=(()=>{class e{doc;_readyPromise;win;backButton=new X;keyboardDidShow=new X;keyboardDidHide=new X;pause=new X;resume=new X;resize=new X;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Xr(this.pause,t,"pause",r),Xr(this.resume,t,"resume",r),Xr(this.backButton,t,"ionBackButton",r),Xr(this.resize,this.win,"resize",r),Xr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Xr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return Ii(this.win,t)}platforms(){return wc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return dN(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(R(se),R(g))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),dN=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Xr=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},dn=(()=>{class e{location;serializer;router;topOutlet;direction=EC;animated=_C;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof Mt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ge(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=fN(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=EC,this.animated=_C,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=I({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(R(SC),R($e),R(Ht),R(De,8))};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),fN=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},EC="auto",_C=void 0,Ei=(()=>{class e{get(t,r){let o=qf();return o?o.get(t,r):null}getBoolean(t,r){let o=qf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=qf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Sc=new A("USERCONFIG"),qf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},wi=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},fn=(()=>{class e{zone=v(g);applicationRef=v(Dt);config=v(Sc);create(t,r,o){return new Yf(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),Yf=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=I({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=hN(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},hN=(e,n,t,r,o,i,s,a,c,l,d,h)=>{let p=J.create({providers:gN(c),parent:t}),f=iD(a,{environmentInjector:n,elementInjector:p}),y=f.instance,S=f.location.nativeElement;if(c)if(d&&y[d]!==void 0&&console.error(`[Ionic Error]: ${d} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${d}" property from ${a.name}.`),h===!0&&f.setInput!==void 0){let z=c,{modal:xt,popover:ce}=z,Gn=Ac(z,["modal","popover"]);for(let Mi in Gn)f.setInput(Mi,Gn[Mi]);xt!==void 0&&Object.assign(y,{modal:xt}),ce!==void 0&&Object.assign(y,{popover:ce})}else Object.assign(y,c);if(l)for(let xt of l)S.classList.add(xt);let N=TC(e,y,S);return s.appendChild(S),r.attachView(f.hostView),o.set(S,f),i.set(S,N),S},pN=[Oc,kc,Fc,Pc,Lc],TC=(e,n,t)=>e.run(()=>{let r=pN.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),MC=new A("NavParamsToken"),gN=e=>[{provide:MC,useValue:e},{provide:wi,useFactory:mN,deps:[MC]}],mN=e=>new wi(e),vN=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},yN=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},oh=(e,n,t)=>{t.forEach(r=>e[r]=Dn(n,r))};function Tc(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&vN(t,o),i&&yN(t,i),t}}var DN=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],IN=["present","dismiss","onDidDismiss","onWillDismiss"],xC=(()=>{let e=class Qf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),oh(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Qf)(u(D),u(m),u(g))};static \u0275dir=V({type:Qf,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&nn(i,yt,5),r&2){let s;it(s=st())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=w([Tc({inputs:DN,methods:IN})],e),e})(),CN=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],bN=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],AC=(()=>{let e=class Kf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),oh(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Kf)(u(D),u(m),u(g))};static \u0275dir=V({type:Kf,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&nn(i,yt,5),r&2){let s;it(s=st())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=w([Tc({inputs:CN,methods:bN})],e),e})(),wN=(e,n,t)=>t==="root"?RC(e,n):t==="forward"?EN(e,n):_N(e,n),RC=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),EN=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),_N=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):RC(e,n),Xf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},NC=(e,n)=>n?e.stackId!==n.stackId:!0,MN=(e,n)=>{if(!e)return;let t=OC(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},OC=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),kC=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Jf=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?OC(n):void 0}createView(n,t){let r=Xf(this.router,t),o=n?.location?.nativeElement,i=TC(this.zone,n.instance,o);return{id:this.nextId++,stackId:MN(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=Xf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=NC(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),l,d=this.router;d.getCurrentNavigation?l=d.getCurrentNavigation():d.navigations?.value&&(l=d.navigations.value),l?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let h=this.views.includes(n),p=this.insertView(n,r);h||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>SN(n,p,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,P(I({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&FC(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(kC),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=wN(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,l=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),l.commit)?l.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ge(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},SN=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{FC(e,n,t,r,o),i()})}):Promise.resolve(),FC=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(kC)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},ih=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new de(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=F;stackWillChange=new oe;stackDidChange=new oe;activateEvents=new oe;deactivateEvents=new oe;parentContexts=v(Tt);location=v(Ue);environmentInjector=v(ie);inputBinder=v(PC,{optional:!0});supportsBindingToComponentInputs=!0;config=v(Ei);navCtrl=v(dn);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,l){this.parentOutlet=l,this.nativeEl=i.nativeElement,this.name=t||F,this.tabsPrefix=r==="true"?Xf(s,c):void 0,this.stackCtrl=new Jf(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>hn(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=I({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,l=new de(null),d=this.createActivatedRouteProxy(l,t),h=new eh(d,c,this.location.injector),p=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(p,{index:this.outletContent.length,injector:h,environmentInjector:r??this.environmentInjector}),l.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,d),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:NC(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Te;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(me(o=>!!o),ve(o=>this.currentActivatedRoute$.pipe(me(i=>i!==null&&i.component===o),ve(i=>i&&i.activatedRoute[r]),il())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Ct("name"),Ct("tabs"),u($e),u(m),u(De),u(g),u(Te),u(e,12))};static \u0275dir=V({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),eh=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Te?this.route:n===Tt?this.childContexts:this.parent.get(n,t)}},PC=new A(""),TN=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=yn([r.queryParams,r.params,r.data]).pipe(ve(([i,s,a],c)=>(a=I(I(I({},i),s),a),c===0?O(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=Ca(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})(),LC=()=>({provide:PC,useFactory:xN,deps:[De]});function xN(e){return e?.componentInputBindingEnabled?new TN:null}var AN=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],jC=(()=>{let e=class th{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||th)(u(ih,8),u(dn),u(Ei),u(m),u(g),u(D))};static \u0275dir=V({type:th,hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=w([Tc({inputs:AN})],e),e})(),VC=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(u(Le),u(dn),u(m),u(De),u(hi,8))};static \u0275dir=V({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&ye("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Xe]})}return e})(),BC=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(u(Le),u(dn),u(m),u(De),u(hi,8))};static \u0275dir=V({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&ye("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Xe]})}return e})(),RN=["animated","animation","root","rootParams","swipeGesture"],NN=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],UC=(()=>{let e=class nh{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),oh(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||nh)(u(m),u(ie),u(J),u(fn),u(g),u(D))};static \u0275dir=V({type:nh,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=w([Tc({inputs:RN,methods:NN})],e),e})(),HC=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new oe;ionTabsDidChange=new oe;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let l=this.outlet.getRootView(o),d=l&&s===l.url&&l.savedExtras;return this.navCtrl.navigateRoot(s,P(I({},d),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,l=a?.savedExtras;return this.navCtrl.navigateRoot(c,P(I({},l),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(u(dn))};static \u0275dir=V({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&Fo(uN,7,m),r&2){let i;it(i=st())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&ye("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),sh=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),_i=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,zn(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),zn(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),zn(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Un)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>zn(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),zn(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(u(J),u(m))};static \u0275dir=V({type:e,hostBindings:function(r,o){r&1&&ye("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),zn=e=>{sh(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=ON(n);Zf(n,r);let o=n.closest("ion-item");o&&(t?Zf(o,[...r,"item-has-value"]):Zf(o,r))})},ON=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&kN(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},Zf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},kN=(e,n)=>e.substring(0,n.length)===n,rh=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},un=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};function WC(){var e=[];if(typeof window<"u"){var n=window;(!n.customElements||n.Element&&(!n.Element.prototype.closest||!n.Element.prototype.matches||!n.Element.prototype.remove||!n.Element.prototype.getRootNode))&&e.push(import("./chunk-5X4HMWFG.js"));var t=function(){try{var r=new URL("b","http://a");return r.pathname="c%20d",r.href==="http://a/c%20d"&&r.searchParams}catch{return!1}};(typeof Object.assign!="function"||!Object.entries||!Array.prototype.find||!Array.prototype.includes||!String.prototype.startsWith||!String.prototype.endsWith||n.NodeList&&!n.NodeList.prototype.forEach||!n.fetch||!t()||typeof WeakMap>"u")&&e.push(import("./chunk-LQX7KJ2R.js"))}return Promise.all(e)}var qC=Vc;var ZC=(e,n)=>ge(void 0,null,function*(){if(!(typeof window>"u"))return yield qC(),Wp(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},null,{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16],"beforeLeave":[16],"beforeEnter":[16]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16],"readonly":[4],"isDateEnabled":[16],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16],"multiple":[4],"highlightedDates":[16],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16],"leaveAnimation":[16],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16],"htmlAttributes":[16],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16],"leaveAnimation":[16],"component":[1],"componentProps":[16],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),n)});var _=["*"],WN=["outletContent"],qN=["outlet"],ZN=[[["","slot","top"]],"*",[["ion-tab"]]],YN=["[slot=top]","*","ion-tab"];function QN(e,n){if(e&1){let t=qy();Rr(0,"ion-router-outlet",5,1),ye("stackWillChange",function(o){Zu(t);let i=ko();return Yu(i.onStackWillChange(o))})("stackDidChange",function(o){Zu(t);let i=ko();return Yu(i.onStackDidChange(o))}),Nr()}}function KN(e,n){e&1&&b(0,2,["*ngIf","tabs.length > 0"])}function XN(e,n){if(e&1&&(Rr(0,"div",1),Da(1,2),Nr()),e&2){let t=ko();aa(),tn("ngTemplateOutlet",t.template)}}function JN(e,n){if(e&1&&Da(0,1),e&2){let t=ko();tn("ngTemplateOutlet",t.template)}}var eO=(()=>{class e extends _i{constructor(t,r){super(t,r)}writeValue(t){this.elementRef.nativeElement.checked=this.lastValue=t,zn(this.elementRef)}_handleIonChange(t){this.handleValueChange(t,t.checked)}static \u0275fac=function(r){return new(r||e)(u(J),u(m))};static \u0275dir=V({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(r,o){r&1&&ye("ionChange",function(s){return o._handleIonChange(s.target)})},standalone:!1,features:[Ae([{provide:Hn,useExisting:e,multi:!0}]),ee]})}return e})(),tO=(()=>{class e extends _i{el;constructor(t,r){super(t,r),this.el=r}handleInputEvent(t){this.handleValueChange(t,t.value)}registerOnChange(t){this.el.nativeElement.tagName==="ION-INPUT"?super.registerOnChange(r=>{t(r===""?null:parseFloat(r))}):super.registerOnChange(t)}static \u0275fac=function(r){return new(r||e)(u(J),u(m))};static \u0275dir=V({type:e,selectors:[["ion-input","type","number"],["ion-range"]],hostBindings:function(r,o){r&1&&ye("ionInput",function(s){return o.handleInputEvent(s.target)})},standalone:!1,features:[Ae([{provide:Hn,useExisting:e,multi:!0}]),ee]})}return e})(),nO=(()=>{class e extends _i{constructor(t,r){super(t,r)}_handleChangeEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(J),u(m))};static \u0275dir=V({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(r,o){r&1&&ye("ionChange",function(s){return o._handleChangeEvent(s.target)})},standalone:!1,features:[Ae([{provide:Hn,useExisting:e,multi:!0}]),ee]})}return e})(),rO=(()=>{class e extends _i{constructor(t,r){super(t,r)}_handleInputEvent(t){this.handleValueChange(t,t.value)}static \u0275fac=function(r){return new(r||e)(u(J),u(m))};static \u0275dir=V({type:e,selectors:[["ion-input",3,"type","number"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(r,o){r&1&&ye("ionInput",function(s){return o._handleInputEvent(s.target)})},standalone:!1,features:[Ae([{provide:Hn,useExisting:e,multi:!0}]),ee]})}return e})(),oO=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)},configurable:!0})})},iO=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},Y=(e,n,t)=>{t.forEach(r=>e[r]=Dn(n,r))};function M(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&oO(t,o),i&&iO(t,i),t}}var sO=(()=>{let e=class ah{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ah)(u(D),u(m),u(g))};static \u0275cmp=C({type:ah,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),aO=(()=>{let e=class ch{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||ch)(u(D),u(m),u(g))};static \u0275cmp=C({type:ch,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),cO=(()=>{let e=class lh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||lh)(u(D),u(m),u(g))};static \u0275cmp=C({type:lh,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),lO=(()=>{let e=class uh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||uh)(u(D),u(m),u(g))};static \u0275cmp=C({type:uh,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),uO=(()=>{let e=class dh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||dh)(u(D),u(m),u(g))};static \u0275cmp=C({type:dh,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({methods:["setFocus"]})],e),e})(),dO=(()=>{let e=class fh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||fh)(u(D),u(m),u(g))};static \u0275cmp=C({type:fh,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),fO=(()=>{let e=class hh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionBackdropTap"])}static \u0275fac=function(r){return new(r||hh)(u(D),u(m),u(g))};static \u0275cmp=C({type:hh,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["stopPropagation","tappable","visible"]})],e),e})(),hO=(()=>{let e=class ph{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ph)(u(D),u(m),u(g))};static \u0275cmp=C({type:ph,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),pO=(()=>{let e=class gh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||gh)(u(D),u(m),u(g))};static \u0275cmp=C({type:gh,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),gO=(()=>{let e=class mh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(r){return new(r||mh)(u(D),u(m),u(g))};static \u0275cmp=C({type:mh,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),mO=(()=>{let e=class vh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||vh)(u(D),u(m),u(g))};static \u0275cmp=C({type:vh,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),vO=(()=>{let e=class yh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||yh)(u(D),u(m),u(g))};static \u0275cmp=C({type:yh,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse"]})],e),e})(),yO=(()=>{let e=class Dh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Dh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Dh,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),DO=(()=>{let e=class Ih{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ih)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ih,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["mode"]})],e),e})(),IO=(()=>{let e=class Ch{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ch)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ch,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","translucent"]})],e),e})(),CO=(()=>{let e=class bh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||bh)(u(D),u(m),u(g))};static \u0275cmp=C({type:bh,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),bO=(()=>{let e=class wh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wh)(u(D),u(m),u(g))};static \u0275cmp=C({type:wh,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),wO=(()=>{let e=class Eh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Eh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Eh,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),EO=(()=>{let e=class _h{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_h)(u(D),u(m),u(g))};static \u0275cmp=C({type:_h,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","outline"]})],e),e})(),_O=(()=>{let e=class Mh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Mh,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),MO=(()=>{let e=class Sh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(r){return new(r||Sh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Sh,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),SO=(()=>{let e=class Th{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Th)(u(D),u(m),u(g))};static \u0275cmp=C({type:Th,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),TO=(()=>{let e=class xh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||xh)(u(D),u(m),u(g))};static \u0275cmp=C({type:xh,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","datetime","disabled","mode"]})],e),e})(),xO=(()=>{let e=class Ah{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ah)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ah,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),AO=(()=>{let e=class Rh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Rh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Rh,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),RO=(()=>{let e=class Nh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Nh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Nh,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activated","side"]})],e),e})(),NO=(()=>{let e=class Oh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Oh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Oh,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse","mode","translucent"]})],e),e})(),OO=(()=>{let e=class kh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||kh)(u(D),u(m),u(g))};static \u0275cmp=C({type:kh,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["fixed"]})],e),e})(),kO=(()=>{let e=class Fh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Fh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Fh,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["collapse","mode","translucent"]})],e),e})(),FO=(()=>{let e=class Ph{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ph)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ph,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),PO=(()=>{let e=class Lh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(r){return new(r||Lh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Lh,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alt","src"]})],e),e})(),LO=(()=>{let e=class jh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInfinite"])}static \u0275fac=function(r){return new(r||jh)(u(D),u(m),u(g))};static \u0275cmp=C({type:jh,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),jO=(()=>{let e=class Vh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Vh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Vh,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["loadingSpinner","loadingText"]})],e),e})(),VO=(()=>{let e=class Bh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Bh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Bh,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),BO=(()=>{let e=class Uh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Uh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Uh,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),UO=(()=>{let e=class Hh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Hh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Hh,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),HO=(()=>{let e=class $h{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||$h)(u(D),u(m),u(g))};static \u0275cmp=C({type:$h,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","sticky"]})],e),e})(),$O=(()=>{let e=class zh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||zh)(u(D),u(m),u(g))};static \u0275cmp=C({type:zh,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),zO=(()=>{let e=class Gh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Gh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Gh,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),GO=(()=>{let e=class Wh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSwipe"])}static \u0275fac=function(r){return new(r||Wh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Wh,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["side"]})],e),e})(),WO=(()=>{let e=class qh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionDrag"])}static \u0275fac=function(r){return new(r||qh)(u(D),u(m),u(g))};static \u0275cmp=C({type:qh,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),qO=(()=>{let e=class Zh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Zh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Zh,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","position"]})],e),e})(),ZO=(()=>{let e=class Yh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Yh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Yh,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),YO=(()=>{let e=class Qh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Qh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Qh,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","lines","mode"]})],e),e})(),QO=(()=>{let e=class Kh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Kh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Kh,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),KO=(()=>{let e=class Xh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(r){return new(r||Xh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Xh,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),XO=(()=>{let e=class Jh{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Jh)(u(D),u(m),u(g))};static \u0275cmp=C({type:Jh,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),JO=(()=>{let e=class ep{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ep)(u(D),u(m),u(g))};static \u0275cmp=C({type:ep,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoHide","menu"]})],e),e})(),ek=(()=>{let e=class tp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||tp)(u(D),u(m),u(g))};static \u0275cmp=C({type:tp,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),tk=(()=>{let e=class np{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||np)(u(D),u(m),u(g))};static \u0275cmp=C({type:np,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),nk=(()=>{let e=class rp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||rp)(u(D),u(m),u(g))};static \u0275cmp=C({type:rp,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["mode"]})],e),e})(),rk=(()=>{let e=class op{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||op)(u(D),u(m),u(g))};static \u0275cmp=C({type:op,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),ok=(()=>{let e=class ip{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ip)(u(D),u(m),u(g))};static \u0275cmp=C({type:ip,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","value"]})],e),e})(),ik=(()=>{let e=class sp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||sp)(u(D),u(m),u(g))};static \u0275cmp=C({type:sp,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),sk=(()=>{let e=class ap{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||ap)(u(D),u(m),u(g))};static \u0275cmp=C({type:ap,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),ak=(()=>{let e=class cp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||cp)(u(D),u(m),u(g))};static \u0275cmp=C({type:cp,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),ck=(()=>{let e=class lp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||lp)(u(D),u(m),u(g))};static \u0275cmp=C({type:lp,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),lk=(()=>{let e=class up{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(r){return new(r||up)(u(D),u(m),u(g))};static \u0275cmp=C({type:up,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),uk=(()=>{let e=class dp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(r){return new(r||dp)(u(D),u(m),u(g))};static \u0275cmp=C({type:dp,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),dk=(()=>{let e=class fp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||fp)(u(D),u(m),u(g))};static \u0275cmp=C({type:fp,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),fk=(()=>{let e=class hp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||hp)(u(D),u(m),u(g))};static \u0275cmp=C({type:hp,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),hk=(()=>{let e=class pp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionItemReorder"])}static \u0275fac=function(r){return new(r||pp)(u(D),u(m),u(g))};static \u0275cmp=C({type:pp,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"],methods:["complete"]})],e),e})(),pk=(()=>{let e=class gp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||gp)(u(D),u(m),u(g))};static \u0275cmp=C({type:gp,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["type"],methods:["addRipple"]})],e),e})(),gk=(()=>{let e=class mp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||mp)(u(D),u(m),u(g))};static \u0275cmp=C({type:mp,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),mk=(()=>{let e=class vp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||vp)(u(D),u(m),u(g))};static \u0275cmp=C({type:vp,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),vk=(()=>{let e=class yp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange"])}static \u0275fac=function(r){return new(r||yp)(u(D),u(m),u(g))};static \u0275cmp=C({type:yp,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),yk=(()=>{let e=class Dp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Dp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Dp,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),Dk=(()=>{let e=class Ip{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ip)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ip,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),Ik=(()=>{let e=class Cp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(r){return new(r||Cp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Cp,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled"]})],e),e})(),Ck=(()=>{let e=class bp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||bp)(u(D),u(m),u(g))};static \u0275cmp=C({type:bp,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),bk=(()=>{let e=class wp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||wp)(u(D),u(m),u(g))};static \u0275cmp=C({type:wp,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["header","multiple","options"]})],e),e})(),wk=(()=>{let e=class Ep{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ep)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ep,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","value"]})],e),e})(),Ek=(()=>{let e=class _p{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||_p)(u(D),u(m),u(g))};static \u0275cmp=C({type:_p,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated"]})],e),e})(),_k=(()=>{let e=class Mp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Mp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Mp,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","duration","name","paused"]})],e),e})(),Mk=(()=>{let e=class Sp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(r){return new(r||Sp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Sp,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["contentId","disabled","when"]})],e),e})(),YC=(()=>{let e=class Tp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Tp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Tp,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["component","tab"],methods:["setActive"]})],e),e})(),xp=(()=>{let e=class Ap{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Ap)(u(D),u(m),u(g))};static \u0275cmp=C({type:Ap,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),Sk=(()=>{let e=class Rp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Rp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Rp,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),Tk=(()=>{let e=class Np{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Np)(u(D),u(m),u(g))};static \u0275cmp=C({type:Np,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),xk=(()=>{let e=class Op{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(r){return new(r||Op)(u(D),u(m),u(g))};static \u0275cmp=C({type:Op,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),Ak=(()=>{let e=class kp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||kp)(u(D),u(m),u(g))};static \u0275cmp=C({type:kp,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({})],e),e})(),Rk=(()=>{let e=class Fp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||Fp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Fp,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","size"]})],e),e})(),Nk=(()=>{let e=class Pp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Pp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Pp,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Ok=(()=>{let e=class Lp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement,Y(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(r){return new(r||Lp)(u(D),u(m),u(g))};static \u0275cmp=C({type:Lp,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),kk=(()=>{let e=class jp{z;el;constructor(t,r,o){this.z=o,t.detach(),this.el=r.nativeElement}static \u0275fac=function(r){return new(r||jp)(u(D),u(m),u(g))};static \u0275cmp=C({type:jp,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})};return e=w([M({inputs:["color","mode"]})],e),e})(),xc=(()=>{class e extends ih{parentOutlet;outletContent;constructor(t,r,o,i,s,a,c,l){super(t,r,o,i,s,a,c,l),this.parentOutlet=l}static \u0275fac=function(r){return new(r||e)(Ct("name"),Ct("tabs"),u($e),u(m),u(De),u(g),u(Te),u(e,12))};static \u0275cmp=C({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(r,o){if(r&1&&Fo(WN,7,Ue),r&2){let i;it(i=st())&&(o.outletContent=i.first)}},standalone:!1,features:[ee],ngContentSelectors:_,decls:3,vars:0,consts:[["outletContent",""]],template:function(r,o){r&1&&(E(),va(0,null,0),b(2),ya())},encapsulation:2})}return e})(),Fk=(()=>{class e extends HC{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-tabs"]],contentQueries:function(r,o,i){if(r&1&&(nn(i,xp,5),nn(i,xp,4),nn(i,YC,4)),r&2){let s;it(s=st())&&(o.tabBar=s.first),it(s=st())&&(o.tabBars=s),it(s=st())&&(o.tabs=s)}},viewQuery:function(r,o){if(r&1&&Fo(qN,5,xc),r&2){let i;it(i=st())&&(o.outlet=i.first)}},standalone:!1,features:[ee],ngContentSelectors:YN,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(r,o){r&1&&(E(ZN),b(0),Rr(1,"div",2,0),No(3,QN,2,0,"ion-router-outlet",3)(4,KN,1,0,"ng-content",4),Nr(),b(5,1)),r&2&&(aa(3),tn("ngIf",o.tabs.length===0),aa(),tn("ngIf",o.tabs.length>0))},dependencies:[Vo,xc],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),Pk=(()=>{class e extends jC{constructor(t,r,o,i,s,a){super(t,r,o,i,s,a)}static \u0275fac=function(r){return new(r||e)(u(xc,8),u(dn),u(Ei),u(m),u(g),u(D))};static \u0275cmp=C({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[ee],ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})}return e})(),Lk=(()=>{class e extends UC{constructor(t,r,o,i,s,a){super(t,r,o,i,s,a)}static \u0275fac=function(r){return new(r||e)(u(m),u(ie),u(J),u(fn),u(g),u(D))};static \u0275cmp=C({type:e,selectors:[["ion-nav"]],standalone:!1,features:[ee],ngContentSelectors:_,decls:1,vars:0,template:function(r,o){r&1&&(E(),b(0))},encapsulation:2,changeDetection:0})}return e})(),jk=(()=>{class e extends VC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[ee]})}return e})(),Vk=(()=>{class e extends BC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[ee]})}return e})(),Bk=(()=>{class e extends AC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-modal"]],standalone:!1,features:[ee],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(r,o){r&1&&No(0,XN,2,1,"div",0),r&2&&tn("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[Vo,Sa],encapsulation:2,changeDetection:0})}return e})(),Uk=(()=>{class e extends xC{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275cmp=C({type:e,selectors:[["ion-popover"]],standalone:!1,features:[ee],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(r,o){r&1&&No(0,JN,1,1,"ng-container",0),r&2&&tn("ngIf",o.isCmpOpen||o.keepContentsMounted)},dependencies:[Vo,Sa],encapsulation:2,changeDetection:0})}return e})(),Hk={provide:$t,useExisting:Be(()=>QC),multi:!0},QC=(()=>{class e extends Vf{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("max",o._enabled?o.max:null)},standalone:!1,features:[Ae([Hk]),ee]})}return e})(),$k={provide:$t,useExisting:Be(()=>KC),multi:!0},KC=(()=>{class e extends Bf{static \u0275fac=(()=>{let t;return function(o){return(t||(t=be(e)))(o||e)}})();static \u0275dir=V({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&ot("min",o._enabled?o.min:null)},standalone:!1,features:[Ae([$k]),ee]})}return e})(),j4=(()=>{class e extends un{constructor(){super(Bc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var V4=(()=>{class e extends un{constructor(){super(Uc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var zk=(()=>{class e extends un{angularDelegate=v(fn);injector=v(J);environmentInjector=v(ie);constructor(){super(Hc)}create(t){return super.create(P(I({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac})}return e})();var Vp=class extends un{angularDelegate=v(fn);injector=v(J);environmentInjector=v(ie);constructor(){super($c)}create(n){return super.create(P(I({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},B4=(()=>{class e extends un{constructor(){super(zc)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=T({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Gk=(e,n,t)=>()=>{let r=n.defaultView;if(r&&typeof window<"u"){Gc(P(I({},e),{_zoneGate:i=>t.run(i)}));let o="__zone_symbol__addEventListener"in n.body?"__zone_symbol__addEventListener":"addEventListener";return WC().then(()=>ZC(r,{exclude:["ion-tabs"],syncQueue:!0,raf:sh,jmp:i=>t.runOutsideAngular(i),ael(i,s,a,c){i[o](s,a,c)},rel(i,s,a,c){i.removeEventListener(s,a,c)}}))}},Wk=[sO,aO,cO,lO,uO,dO,fO,hO,pO,gO,mO,vO,yO,DO,IO,CO,bO,wO,EO,_O,MO,SO,TO,xO,AO,RO,NO,OO,kO,FO,PO,LO,jO,VO,BO,UO,HO,$O,zO,GO,WO,qO,ZO,YO,QO,KO,XO,JO,ek,tk,nk,rk,ok,ik,sk,ak,ck,lk,uk,dk,fk,hk,pk,gk,mk,vk,yk,Dk,Ik,Ck,bk,wk,Ek,_k,Mk,YC,xp,Sk,Tk,xk,Ak,Rk,Nk,Ok,kk],U4=[...Wk,Bk,Uk,eO,tO,nO,rO,Fk,xc,Pk,Lk,jk,Vk,KC,QC],H4=(()=>{class e{static forRoot(t={}){return{ngModule:e,providers:[{provide:Sc,useValue:t},{provide:ga,useFactory:Gk,multi:!0,deps:[Sc,se,g]},fn,LC()]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=xe({type:e});static \u0275inj=Me({providers:[zk,Vp],imports:[Bo]})}return e})();export{W as a,X as b,de as c,LF as d,w as e,er as f,n0 as g,p0 as h,Dn as i,i0 as j,ut as k,N0 as l,T as m,Me as n,R as o,v as p,Zu as q,Yu as r,be as s,YB as t,oe as u,mt as v,QB as w,_E as x,aa as y,u as z,C as A,xe as B,No as C,tn as D,ma as E,Rr as F,Nr as G,Fd as H,qy as I,ye as J,ko as K,PS as L,s2 as M,jS as N,Qy as O,VS as P,BS as Q,a2 as R,US as S,c2 as T,pT as U,hD as V,Vo as W,Bo as X,OT as Y,PT as Z,on as _,kD as $,PD as aa,ix as ba,Te as ca,SI as da,De as ea,kA as fa,jA as ga,cz as ha,lz as ia,IR as ja,wR as ka,dz as la,TR as ma,AR as na,fz as oa,Hf as pa,wz as qa,Ez as ra,Mz as sa,Sz as ta,KR as ua,Bz as va,Uz as wa,Hz as xa,$z as ya,zz as za,yC as Aa,Gz as Ba,Wz as Ca,SC as Da,rh as Ea,un as Fa,eO as Ga,tO as Ha,nO as Ia,rO as Ja,lO as Ka,uO as La,hO as Ma,mO as Na,vO as Oa,yO as Pa,DO as Qa,IO as Ra,CO as Sa,bO as Ta,wO as Ua,EO as Va,MO as Wa,xO as Xa,AO as Ya,RO as Za,NO as _a,kO as $a,FO as ab,VO as bb,UO as cb,HO as db,$O as eb,qO as fb,ZO as gb,tk as hb,sk as ib,uk as jb,dk as kb,mk as lb,vk as mb,yk as nb,Ck as ob,wk as pb,_k as qb,xp as rb,Sk as sb,Tk as tb,Rk as ub,kk as vb,xc as wb,Fk as xb,Bk as yb,j4 as zb,V4 as Ab,zk as Bb,B4 as Cb,H4 as Db};
