import{a as p}from"./chunk-UFD7UJFV.js";import{a as r}from"./chunk-FULEFYAM.js";import{$ as c,_ as l,m as a,o}from"./chunk-QCXYQNJC.js";import{a as n,b as s}from"./chunk-2R6CW7ES.js";var U=(()=>{class e{get apiUrl(){return`${this.envSwitcher?.getCurrentApiUrl()||r.apiUrl}/auth`}constructor(t,i){this.http=t,this.envSwitcher=i,console.log("Auth Service initialized"),console.log("Environment API URL:",r.apiUrl),console.log("Dynamic API URL:",this.envSwitcher.getCurrentApiUrl()),console.log("Final API URL:",this.apiUrl)}getHeaders(){return new l({"Content-Type":"application/json",Accept:"application/json","X-Requested-With":"XMLHttpRequest"})}login(t){return console.log("\u{1F510} Making login request to:",`${this.apiUrl}/auth/login`),console.log("\u{1F4E7} Credentials:",{email:t.email,password:"***"}),this.http.post(`${this.apiUrl}/auth/login`,t,{headers:this.getHeaders()})}register(t){return console.log("\u{1F4DD} Making registration request to:",`${this.apiUrl}/auth/signup`),console.log("\u{1F464} Registration data:",s(n({},t),{password:"***",password_confirmation:"***"})),this.http.post(`${this.apiUrl}/auth/signup`,t,{headers:this.getHeaders()})}setToken(t){localStorage.setItem("token",t),console.log("\u{1F511} Token stored successfully")}static{this.\u0275fac=function(i){return new(i||e)(o(c),o(p))}}static{this.\u0275prov=a({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();export{U as a};
